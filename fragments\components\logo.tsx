import Image from 'next/image'
import { ComponentProps } from 'react'

interface LogoProps extends Omit<ComponentProps<typeof Image>, 'src' | 'alt' | 'width' | 'height'> {
  width?: number
  height?: number
  style?: string // Custom style prop for logo variants
}

export default function Logo({
  width = 232,
  height = 232,
  style,
  ...props
}: LogoProps) {
  // Filter out the custom style prop since Next.js Image doesn't accept string styles
  const { style: _, ...imageProps } = { style, ...props }

  return (
    <Image
      src="/Logo.png"
      alt="Fragments Logo"
      width={width}
      height={height}
      {...imageProps}
    />
  )
}
