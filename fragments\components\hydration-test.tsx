'use client'

import { useEffect, useState } from 'react'
import { useLocalStorage } from 'usehooks-ts'

export function HydrationTest() {
  const [mounted, setMounted] = useState(false)
  const [testValue, setTestValue] = useLocalStorage('hydration-test', 'default')

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return <div>Loading hydration test...</div>
  }

  return (
    <div className="p-4 border rounded">
      <h3>Hydration Test</h3>
      <p>Current value: {testValue}</p>
      <button 
        onClick={() => setTestValue(Date.now().toString())}
        className="px-4 py-2 bg-blue-500 text-white rounded"
      >
        Update Value
      </button>
    </div>
  )
}
