{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/lib/models.ts"], "sourcesContent": ["import { createAnthropic } from '@ai-sdk/anthropic'\r\nimport { createGoogleGenerativeAI } from '@ai-sdk/google'\r\nimport { createVertex } from '@ai-sdk/google-vertex'\r\nimport { createMistral } from '@ai-sdk/mistral'\r\nimport { createOpenAI } from '@ai-sdk/openai'\r\nimport { createOllama } from 'ollama-ai-provider'\r\nimport { createFireworks } from '@ai-sdk/fireworks'\r\n\r\nexport type LLMModel = {\r\n  id: string\r\n  name: string\r\n  provider: string\r\n  providerId: string\r\n}\r\n\r\nexport type LLMModelConfig = {\r\n  model?: string\r\n  apiKey?: string\r\n  baseURL?: string\r\n  temperature?: number\r\n  topP?: number\r\n  topK?: number\r\n  frequencyPenalty?: number\r\n  presencePenalty?: number\r\n  maxTokens?: number\r\n}\r\n\r\nexport function getModelClient(model: LLMModel, config: LLMModelConfig) {\r\n  const { id: modelNameString, providerId } = model\r\n  const { apiKey, baseURL } = config\r\n\r\n  const providerConfigs = {\r\n    anthropic: () => createAnthropic({ apiKey, baseURL })(modelNameString),\r\n    openai: () => createOpenAI({ apiKey, baseURL })(modelNameString),\r\n    google: () =>\r\n      createGoogleGenerativeAI({ apiKey, baseURL })(modelNameString),\r\n    mistral: () => createMistral({ apiKey, baseURL })(modelNameString),\r\n    groq: () =>\r\n      createOpenAI({\r\n        apiKey: apiKey || process.env.GROQ_API_KEY,\r\n        baseURL: baseURL || 'https://api.groq.com/openai/v1',\r\n      })(modelNameString),\r\n    togetherai: () =>\r\n      createOpenAI({\r\n        apiKey: apiKey || process.env.TOGETHER_API_KEY,\r\n        baseURL: baseURL || 'https://api.together.xyz/v1',\r\n      })(modelNameString),\r\n    ollama: () => createOllama({ baseURL })(modelNameString),\r\n    fireworks: () =>\r\n      createFireworks({\r\n        apiKey: apiKey || process.env.FIREWORKS_API_KEY,\r\n        baseURL: baseURL || 'https://api.fireworks.ai/inference/v1',\r\n      })(modelNameString),\r\n    vertex: () =>\r\n      createVertex({\r\n        googleAuthOptions: {\r\n          credentials: JSON.parse(\r\n            process.env.GOOGLE_VERTEX_CREDENTIALS || '{}',\r\n          ),\r\n        },\r\n      })(modelNameString),\r\n    xai: () =>\r\n      createOpenAI({\r\n        apiKey: apiKey || process.env.XAI_API_KEY,\r\n        baseURL: baseURL || 'https://api.x.ai/v1',\r\n      })(modelNameString),\r\n    deepseek: () =>\r\n      createOpenAI({\r\n        apiKey: apiKey || process.env.DEEPSEEK_API_KEY,\r\n        baseURL: baseURL || 'https://api.deepseek.com/v1',\r\n      })(modelNameString),\r\n  }\r\n\r\n  const createClient =\r\n    providerConfigs[providerId as keyof typeof providerConfigs]\r\n\r\n  if (!createClient) {\r\n    throw new Error(`Unsupported provider: ${providerId}`)\r\n  }\r\n\r\n  return createClient()\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AA2BO,SAAS,eAAe,KAAe,EAAE,MAAsB;IACpE,MAAM,EAAE,IAAI,eAAe,EAAE,UAAU,EAAE,GAAG;IAC5C,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG;IAE5B,MAAM,kBAAkB;QACtB,WAAW,IAAM,CAAA,GAAA,4JAAA,CAAA,kBAAe,AAAD,EAAE;gBAAE;gBAAQ;YAAQ,GAAG;QACtD,QAAQ,IAAM,CAAA,GAAA,yJAAA,CAAA,eAAY,AAAD,EAAE;gBAAE;gBAAQ;YAAQ,GAAG;QAChD,QAAQ,IACN,CAAA,GAAA,yJAAA,CAAA,2BAAwB,AAAD,EAAE;gBAAE;gBAAQ;YAAQ,GAAG;QAChD,SAAS,IAAM,CAAA,GAAA,0JAAA,CAAA,gBAAa,AAAD,EAAE;gBAAE;gBAAQ;YAAQ,GAAG;QAClD,MAAM,IACJ,CAAA,GAAA,yJAAA,CAAA,eAAY,AAAD,EAAE;gBACX,QAAQ,UAAU,QAAQ,GAAG,CAAC,YAAY;gBAC1C,SAAS,WAAW;YACtB,GAAG;QACL,YAAY,IACV,CAAA,GAAA,yJAAA,CAAA,eAAY,AAAD,EAAE;gBACX,QAAQ,UAAU,QAAQ,GAAG,CAAC,gBAAgB;gBAC9C,SAAS,WAAW;YACtB,GAAG;QACL,QAAQ,IAAM,CAAA,GAAA,4JAAA,CAAA,eAAY,AAAD,EAAE;gBAAE;YAAQ,GAAG;QACxC,WAAW,IACT,CAAA,GAAA,4JAAA,CAAA,kBAAe,AAAD,EAAE;gBACd,QAAQ,UAAU,QAAQ,GAAG,CAAC,iBAAiB;gBAC/C,SAAS,WAAW;YACtB,GAAG;QACL,QAAQ,IACN,CAAA,GAAA,mKAAA,CAAA,eAAY,AAAD,EAAE;gBACX,mBAAmB;oBACjB,aAAa,KAAK,KAAK,CACrB,QAAQ,GAAG,CAAC,yBAAyB,IAAI;gBAE7C;YACF,GAAG;QACL,KAAK,IACH,CAAA,GAAA,yJAAA,CAAA,eAAY,AAAD,EAAE;gBACX,QAAQ,UAAU,QAAQ,GAAG,CAAC,WAAW;gBACzC,SAAS,WAAW;YACtB,GAAG;QACL,UAAU,IACR,CAAA,GAAA,yJAAA,CAAA,eAAY,AAAD,EAAE;gBACX,QAAQ,UAAU,QAAQ,GAAG,CAAC,gBAAgB;gBAC9C,SAAS,WAAW;YACtB,GAAG;IACP;IAEA,MAAM,eACJ,eAAe,CAAC,WAA2C;IAE7D,IAAI,CAAC,cAAc;QACjB,MAAM,IAAI,MAAM,CAAC,sBAAsB,EAAE,WAAW,CAAC;IACvD;IAEA,OAAO;AACT"}}, {"offset": {"line": 78, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/lib/templates.ts"], "sourcesContent": ["import templates from './templates.json'\r\n\r\nexport default templates\r\nexport type Templates = typeof templates\r\nexport type TemplateId = keyof typeof templates\r\nexport type TemplateConfig = typeof templates[TemplateId]\r\n\r\nexport function templatesToPrompt(templates: Templates) {\r\n  return `${Object.entries(templates).map(([id, t], index) => `${index + 1}. ${id}: \"${t.instructions}\". File: ${t.file || 'none'}. Dependencies installed: ${t.lib.join(', ')}. Port: ${t.port || 'none'}.`).join('\\n')}`\r\n}\r\n"], "names": [], "mappings": ";;;;;;;uCAEe,wFAAA,CAAA,UAAS;AAKjB,SAAS,kBAAkB,SAAoB;IACpD,OAAO,CAAC,EAAE,OAAO,OAAO,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,EAAE,QAAU,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,YAAY,CAAC,SAAS,EAAE,EAAE,IAAI,IAAI,OAAO,0BAA0B,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,QAAQ,EAAE,EAAE,IAAI,IAAI,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC;AAC1N"}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 103, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/lib/prompt.ts"], "sourcesContent": ["import { Templates, templatesToPrompt } from '@/lib/templates'\r\n\r\nexport function to<PERSON>rom<PERSON>(template: Templates) {\r\n  return `\r\n    Solve the math, physics, chemistry or biology problems. \r\n    FOR ALL PROBLEMS\r\n    USe streamlit with scientific libraries for graphical solutions.\r\n    Do not mention the code libraries used\r\n    FOR MATH AND PHTYSICS \r\n    Use latex for equations\r\n    Use mathplotlib, seaborn, and plotly for data visualization.\r\n    Use numpy and scipy for numerical solutions.\r\n    Use sympy for symbolic solutions.\r\n    Use pandas for data manipulation.\r\n    Use physics libraries for animations and simulations.\r\n    Always include a graphical solution or aniamtion to understand the problem\r\n\r\n    FOR CHEMISTRY: \r\n    Use rdkit for chemical reactions and 2D chemistry problems. \r\n    Include the chemical reaqctions in 2d and with latex.\r\n    \r\n    For BIOLOGY:\r\n    Use biopython for biology problems\r\n    \r\n\r\n\r\n\r\n\r\n    You do not make mistakes.\r\n    You can install additional dependencies.\r\n    Do not touch project dependencies files like package.json, package-lock.json, requirements.txt, etc.\r\n    Do not wrap code in backticks.\r\n    Always break the lines correctly.\r\n    You can use one of the following templates:\r\n    ${templatesToPrompt(template)}\r\n  `\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAEO,SAAS,SAAS,QAAmB;IAC1C,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA+BN,EAAE,CAAA,GAAA,kHAAA,CAAA,oBAAiB,AAAD,EAAE,UAAU;EAChC,CAAC;AACH"}}, {"offset": {"line": 144, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 149, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/lib/ratelimit.ts"], "sourcesContent": ["import { Duration } from './duration'\r\nimport { Ratelimit } from '@upstash/ratelimit'\r\nimport { kv } from '@vercel/kv'\r\n\r\nexport default async function ratelimit(\r\n  key: string | null,\r\n  maxRequests: number,\r\n  window: Duration,\r\n) {\r\n  if (process.env.KV_REST_API_URL && process.env.KV_REST_API_TOKEN) {\r\n    const ratelimit = new Ratelimit({\r\n      redis: kv,\r\n      limiter: Ratelimit.slidingWindow(maxRequests, window),\r\n    })\r\n\r\n    const { success, limit, reset, remaining } = await ratelimit.limit(\r\n      `ratelimit_${key}`,\r\n    )\r\n\r\n    if (!success) {\r\n      return {\r\n        amount: limit,\r\n        reset,\r\n        remaining,\r\n      }\r\n    }\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;AAIe,eAAe,UAC5B,GAAkB,EAClB,WAAmB,EACnB,MAAgB;IAEhB,IAAI,QAAQ,GAAG,CAAC,eAAe,IAAI,QAAQ,GAAG,CAAC,iBAAiB,EAAE;QAChE,MAAM,YAAY,IAAI,yJAAA,CAAA,YAAS,CAAC;YAC9B,OAAO,iJAAA,CAAA,KAAE;YACT,SAAS,yJAAA,CAAA,YAAS,CAAC,aAAa,CAAC,aAAa;QAChD;QAEA,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,MAAM,UAAU,KAAK,CAChE,CAAC,UAAU,EAAE,IAAI,CAAC;QAGpB,IAAI,CAAC,SAAS;YACZ,OAAO;gBACL,QAAQ;gBACR;gBACA;YACF;QACF;IACF;AACF"}}, {"offset": {"line": 173, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 178, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/lib/schema.ts"], "sourcesContent": ["import { z } from 'zod'\r\n\r\nexport const fragmentSchema = z.object({\r\n  commentary: z.string().describe(`Describe what you're about to do and the steps you want to take for generating the fragment in great detail.`),\r\n  template: z.string().describe('Name of the template used to generate the fragment.'),\r\n  // template_ready: z.boolean().describe('Detect if finished identifying the template.'),\r\n  title: z.string().describe('Short title of the fragment. Max 3 words.'),\r\n  description: z.string().describe('Short description of the fragment. Max 1 sentence.'),\r\n  additional_dependencies: z.array(z.string()).describe('Additional dependencies required by the fragment. Do not include dependencies that are already included in the template.'),\r\n  has_additional_dependencies: z.boolean().describe('Detect if additional dependencies that are not included in the template are required by the fragment.'),\r\n  install_dependencies_command: z.string().describe('Command to install additional dependencies required by the fragment.'),\r\n  // install_dependencies_ready: z.boolean().describe('Detect if finished identifying additional dependencies.'),\r\n  port: z.number().nullable().describe('Port number used by the resulted fragment. Null when no ports are exposed.'),\r\n  file_path: z.string().describe('Relative path to the file, including the file name.'),\r\n  code: z.string().describe('Code generated by the fragment. Only runnable code is allowed.'),\r\n  // code: z.array(z.object({\r\n  //   file_name: z.string().describe('Name of the file.'),\r\n  //   file_path: z.string().describe('Relative path to the file, including the file name.'),\r\n  //   file_content: z.string().describe('Content of the file.'),\r\n  //   file_finished: z.boolean().describe('Detect if finished generating the file.'),\r\n  // })),\r\n  // code_finished: z.boolean().describe('Detect if finished generating the code.'),\r\n  // error: z.string().optional().describe('Error message if the fragment is not valid.'),\r\n})\r\n\r\nexport type FragmentSchema = z.infer<typeof fragmentSchema>\r\n"], "names": [], "mappings": ";;;;;;AAEO,MAAM,iBAAiB,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACrC,YAAY,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC,CAAC,4GAA4G,CAAC;IAC9I,UAAU,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC9B,wFAAwF;IACxF,OAAO,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC3B,aAAa,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACjC,yBAAyB,sIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;IACtD,6BAA6B,sIAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ,CAAC;IAClD,8BAA8B,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAClD,+GAA+G;IAC/G,MAAM,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;IACrC,WAAW,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC/B,MAAM,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;AAS5B"}}, {"offset": {"line": 198, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 203, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/app/api/chat/route.ts"], "sourcesContent": ["import { Duration } from '@/lib/duration'\r\nimport { getModelClient } from '@/lib/models'\r\nimport { LLMModel, LLMModelConfig } from '@/lib/models'\r\nimport { toPrompt } from '@/lib/prompt'\r\nimport ratelimit from '@/lib/ratelimit'\r\nimport { fragmentSchema as schema } from '@/lib/schema'\r\nimport { Templates } from '@/lib/templates'\r\nimport { streamObject, LanguageModel, CoreMessage } from 'ai'\r\n\r\nexport const maxDuration = 60\r\n\r\nconst rateLimitMaxRequests = process.env.RATE_LIMIT_MAX_REQUESTS\r\n  ? parseInt(process.env.RATE_LIMIT_MAX_REQUESTS)\r\n  : 10\r\nconst ratelimitWindow = process.env.RATE_LIMIT_WINDOW\r\n  ? (process.env.RATE_LIMIT_WINDOW as Duration)\r\n  : '1d'\r\n\r\nexport async function POST(req: Request) {\r\n  const {\r\n    messages,\r\n    userID,\r\n    teamID,\r\n    template,\r\n    model,\r\n    config,\r\n  }: {\r\n    messages: CoreMessage[]\r\n    userID: string | undefined\r\n    teamID: string | undefined\r\n    template: Templates\r\n    model: LLMModel\r\n    config: LLMModelConfig\r\n  } = await req.json()\r\n\r\n  const limit = !config.apiKey\r\n    ? await ratelimit(\r\n        req.headers.get('x-forwarded-for'),\r\n        rateLimitMaxRequests,\r\n        ratelimitWindow,\r\n      )\r\n    : false\r\n\r\n  if (limit) {\r\n    return new Response('You have reached your request limit for the day.', {\r\n      status: 429,\r\n      headers: {\r\n        'X-RateLimit-Limit': limit.amount.toString(),\r\n        'X-RateLimit-Remaining': limit.remaining.toString(),\r\n        'X-RateLimit-Reset': limit.reset.toString(),\r\n      },\r\n    })\r\n  }\r\n\r\n  console.log('userID', userID)\r\n  console.log('teamID', teamID)\r\n  // console.log('template', template)\r\n  console.log('model', model)\r\n  // console.log('config', config)\r\n\r\n  const { model: modelNameString, apiKey: modelApiKey, ...modelParams } = config\r\n  const modelClient = getModelClient(model, config)\r\n\r\n  try {\r\n    const stream = await streamObject({\r\n      model: modelClient as LanguageModel,\r\n      schema,\r\n      system: toPrompt(template),\r\n      messages,\r\n      maxRetries: 0, // do not retry on errors\r\n      ...modelParams,\r\n    })\r\n\r\n    return stream.toTextStreamResponse()\r\n  } catch (error: any) {\r\n    const isRateLimitError =\r\n      error && (error.statusCode === 429 || error.message.includes('limit'))\r\n    const isOverloadedError =\r\n      error && (error.statusCode === 529 || error.statusCode === 503)\r\n    const isAccessDeniedError =\r\n      error && (error.statusCode === 403 || error.statusCode === 401)\r\n\r\n    if (isRateLimitError) {\r\n      return new Response(\r\n        'The provider is currently unavailable due to request limit. Try using your own API key.',\r\n        {\r\n          status: 429,\r\n        },\r\n      )\r\n    }\r\n\r\n    if (isOverloadedError) {\r\n      return new Response(\r\n        'The provider is currently unavailable. Please try again later.',\r\n        {\r\n          status: 529,\r\n        },\r\n      )\r\n    }\r\n\r\n    if (isAccessDeniedError) {\r\n      return new Response(\r\n        'Access denied. Please make sure your API key is valid.',\r\n        {\r\n          status: 403,\r\n        },\r\n      )\r\n    }\r\n\r\n    console.error('Error:', error)\r\n\r\n    return new Response(\r\n      'An unexpected error has occurred. Please try again later.',\r\n      {\r\n        status: 500,\r\n      },\r\n    )\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AASO,MAAM,cAAc;AAE3B,MAAM,uBAAuB,QAAQ,GAAG,CAAC,uBAAuB,GAC5D,SAAS,QAAQ,GAAG,CAAC,uBAAuB,IAC5C;AACJ,MAAM,kBAAkB,QAAQ,GAAG,CAAC,iBAAiB,GAChD,QAAQ,GAAG,CAAC,iBAAiB,GAC9B;AAEG,eAAe,KAAK,GAAY;IACrC,MAAM,EACJ,QAAQ,EACR,MAAM,EACN,MAAM,EACN,QAAQ,EACR,KAAK,EACL,MAAM,EACP,GAOG,MAAM,IAAI,IAAI;IAElB,MAAM,QAAQ,CAAC,OAAO,MAAM,GACxB,MAAM,CAAA,GAAA,kHAAA,CAAA,UAAS,AAAD,EACZ,IAAI,OAAO,CAAC,GAAG,CAAC,oBAChB,sBACA,mBAEF;IAEJ,IAAI,OAAO;QACT,OAAO,IAAI,SAAS,oDAAoD;YACtE,QAAQ;YACR,SAAS;gBACP,qBAAqB,MAAM,MAAM,CAAC,QAAQ;gBAC1C,yBAAyB,MAAM,SAAS,CAAC,QAAQ;gBACjD,qBAAqB,MAAM,KAAK,CAAC,QAAQ;YAC3C;QACF;IACF;IAEA,QAAQ,GAAG,CAAC,UAAU;IACtB,QAAQ,GAAG,CAAC,UAAU;IACtB,oCAAoC;IACpC,QAAQ,GAAG,CAAC,SAAS;IACrB,gCAAgC;IAEhC,MAAM,EAAE,OAAO,eAAe,EAAE,QAAQ,WAAW,EAAE,GAAG,aAAa,GAAG;IACxE,MAAM,cAAc,CAAA,GAAA,+GAAA,CAAA,iBAAc,AAAD,EAAE,OAAO;IAE1C,IAAI;QACF,MAAM,SAAS,MAAM,CAAA,GAAA,sJAAA,CAAA,eAAY,AAAD,EAAE;YAChC,OAAO;YACP,QAAA,+GAAA,CAAA,iBAAM;YACN,QAAQ,CAAA,GAAA,+GAAA,CAAA,WAAQ,AAAD,EAAE;YACjB;YACA,YAAY;YACZ,GAAG,WAAW;QAChB;QAEA,OAAO,OAAO,oBAAoB;IACpC,EAAE,OAAO,OAAY;QACnB,MAAM,mBACJ,SAAS,CAAC,MAAM,UAAU,KAAK,OAAO,MAAM,OAAO,CAAC,QAAQ,CAAC,QAAQ;QACvE,MAAM,oBACJ,SAAS,CAAC,MAAM,UAAU,KAAK,OAAO,MAAM,UAAU,KAAK,GAAG;QAChE,MAAM,sBACJ,SAAS,CAAC,MAAM,UAAU,KAAK,OAAO,MAAM,UAAU,KAAK,GAAG;QAEhE,IAAI,kBAAkB;YACpB,OAAO,IAAI,SACT,2FACA;gBACE,QAAQ;YACV;QAEJ;QAEA,IAAI,mBAAmB;YACrB,OAAO,IAAI,SACT,kEACA;gBACE,QAAQ;YACV;QAEJ;QAEA,IAAI,qBAAqB;YACvB,OAAO,IAAI,SACT,0DACA;gBACE,QAAQ;YACV;QAEJ;QAEA,QAAQ,KAAK,CAAC,UAAU;QAExB,OAAO,IAAI,SACT,6DACA;YACE,QAAQ;QACV;IAEJ;AACF"}}, {"offset": {"line": 276, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}