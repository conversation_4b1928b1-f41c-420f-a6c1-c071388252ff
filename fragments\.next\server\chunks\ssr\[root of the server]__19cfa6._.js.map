{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[next]/internal/font/google/inter_59dee874.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_export_value__({\n  \"className\": \"inter_59dee874-module__9CtR0q__className\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 12, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[next]/internal/font/google/inter_59dee874.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Inter%22,%22arguments%22:[{%22subsets%22:[%22latin%22]}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'__Inter_59dee8', '__Inter_Fallback_59dee8'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;;;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe"}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 34, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/app/providers.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const PostHogProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call PostHogProvider() from the server but PostHogProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/providers.tsx\",\n    \"PostHogProvider\",\n);\nexport const ThemeProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/providers.tsx\",\n    \"ThemeProvider\",\n);\n"], "names": [], "mappings": ";;;;;;;AACO,MAAM,kBAAkB,CAAA,GAAA,+PAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,+BACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,+PAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,+BACA"}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 57, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/components/ui/toaster.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Toaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call Toaster() from the server but To<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/toaster.tsx\",\n    \"Toaster\",\n);\n"], "names": [], "mappings": ";;;;;;AACO,MAAM,UAAU,CAAA,GAAA,+PAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,uCACA"}}, {"offset": {"line": 71, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 81, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/app/layout.tsx"], "sourcesContent": ["import './globals.css'\r\nimport { PostHogProvider, ThemeProvider } from './providers'\r\nimport { Toaster } from '@/components/ui/toaster'\r\nimport { Analytics } from '@vercel/analytics/next'\r\nimport type { Metadata } from 'next'\r\nimport { Inter } from 'next/font/google'\r\n\r\nconst inter = Inter({ subsets: ['latin'] })\r\n\r\nexport const metadata: Metadata = {\r\n  title: 'Fragments by E2B',\r\n  description: \"Open-source version of Anthropic's Artifacts\",\r\n}\r\n\r\nexport default function RootLayout({\r\n  children,\r\n}: Readonly<{\r\n  children: React.ReactNode\r\n}>) {\r\n  return (\r\n    <html lang=\"en\" suppressHydrationWarning>\r\n      <PostHogProvider>\r\n        <body className={inter.className} suppressHydrationWarning>\r\n          <ThemeProvider\r\n            attribute=\"class\"\r\n            defaultTheme=\"dark\"\r\n            enableSystem\r\n            disableTransitionOnChange\r\n          >\r\n            {children}\r\n          </ThemeProvider>\r\n          <Toaster />\r\n          <Analytics />\r\n        </body>\r\n      </PostHogProvider>\r\n    </html>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AASO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,wPAAC;QAAK,MAAK;QAAK,wBAAwB;kBACtC,cAAA,wPAAC,iHAAA,CAAA,kBAAe;sBACd,cAAA,wPAAC;gBAAK,WAAW,yIAAA,CAAA,UAAK,CAAC,SAAS;gBAAE,wBAAwB;;kCACxD,wPAAC,iHAAA,CAAA,gBAAa;wBACZ,WAAU;wBACV,cAAa;wBACb,YAAY;wBACZ,yBAAyB;kCAExB;;;;;;kCAEH,wPAAC,4HAAA,CAAA,UAAO;;;;;kCACR,wPAAC,+JAAA,CAAA,YAAS;;;;;;;;;;;;;;;;;;;;;AAKpB"}}, {"offset": {"line": 153, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 169, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/app/favicon.ico.mjs (structured image object)"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 32, height: 32 }\n"], "names": [], "mappings": ";;;;;;uCACe;IAAE,KAAA,2GAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;AAAG"}}, {"offset": {"line": 180, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 184, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/.next-internal/server/app/_not-found/page/actions.js"], "sourcesContent": ["__turbopack_export_value__({\n});"], "names": [], "mappings": "AAAA,2BAA2B,CAC3B"}}, {"offset": {"line": 185, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}