{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst alertVariants = cva(\r\n  \"relative w-full rounded-lg border px-4 py-3 text-sm [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground [&>svg~*]:pl-7\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-background text-foreground\",\r\n        destructive:\r\n          \"bg-red-400/10 text-red-400 border-red-400/50 [&>svg]:text-red-400\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nconst Alert = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>\r\n>(({ className, variant, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    role=\"alert\"\r\n    className={cn(alertVariants({ variant }), className)}\r\n    {...props}\r\n  />\r\n))\r\nAlert.displayName = \"Alert\"\r\n\r\nconst AlertTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className, ...props }, ref) => (\r\n  <h5\r\n    ref={ref}\r\n    className={cn(\"mb-1 font-medium leading-none tracking-tight\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertTitle.displayName = \"AlertTitle\"\r\n\r\nconst AlertDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm [&_p]:leading-relaxed\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertDescription.displayName = \"AlertDescription\"\r\n\r\nexport { Alert, AlertTitle, AlertDescription }\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAKA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,2KACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,gNAAM,UAAU,CAG5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBACnC,wPAAC;QACC,KAAK;QACL,MAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG;AAEpB,MAAM,2BAAa,gNAAM,UAAU,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,wPAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,iCAAmB,gNAAM,UAAU,CAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,wPAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG"}}, {"offset": {"line": 64, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/components/ui/button.tsx"], "sourcesContent": ["import { cn } from '@/lib/utils'\r\nimport { Slot } from '@radix-ui/react-slot'\r\nimport { cva, type VariantProps } from 'class-variance-authority'\r\nimport * as React from 'react'\r\n\r\nconst buttonVariants = cva(\r\n  'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50',\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          'bg-primary text-primary-foreground shadow hover:bg-primary/90',\r\n        destructive:\r\n          'bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90',\r\n        outline:\r\n          'border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground',\r\n        secondary:\r\n          'bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80',\r\n        ghost: 'hover:bg-accent hover:text-accent-foreground',\r\n        link: 'text-primary underline-offset-4 hover:underline',\r\n      },\r\n      size: {\r\n        default: 'h-9 px-4 py-2',\r\n        sm: 'h-8 rounded-md px-3 text-xs',\r\n        lg: 'h-10 rounded-md px-8',\r\n        icon: 'h-9 w-9',\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: 'default',\r\n      size: 'default',\r\n    },\r\n  },\r\n)\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : 'button'\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  },\r\n)\r\nButton.displayName = 'Button'\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAKA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,uOACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,gNAAM,UAAU,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,wPAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG"}}, {"offset": {"line": 124, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 129, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nexport interface InputProps\r\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\r\n\r\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\r\n  ({ className, type, ...props }, ref) => {\r\n    return (\r\n      <input\r\n        type={type}\r\n        className={cn(\r\n          \"flex h-9 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nInput.displayName = \"Input\"\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;;;;;;;AAOA,MAAM,sBAAQ,gNAAM,UAAU,CAC5B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,wPAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,4TACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG"}}, {"offset": {"line": 153, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 158, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst labelVariants = cva(\r\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\r\n)\r\n\r\nconst Label = React.forwardRef<\r\n  React.ElementRef<typeof LabelPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\r\n    VariantProps<typeof labelVariants>\r\n>(({ className, ...props }, ref) => (\r\n  <LabelPrimitive.Root\r\n    ref={ref}\r\n    className={cn(labelVariants(), className)}\r\n    {...props}\r\n  />\r\n))\r\nLabel.displayName = LabelPrimitive.Root.displayName\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;;;;;AAQA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,gNAAM,UAAU,CAI5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,wPAAC,kKAAe,IAAI;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG,kKAAe,IAAI,CAAC,WAAW"}}, {"offset": {"line": 185, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 190, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Separator = React.forwardRef<\r\n  React.ElementRef<typeof SeparatorPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof SeparatorPrimitive.Root>\r\n>(\r\n  (\r\n    { className, orientation = \"horizontal\", decorative = true, ...props },\r\n    ref\r\n  ) => (\r\n    <SeparatorPrimitive.Root\r\n      ref={ref}\r\n      decorative={decorative}\r\n      orientation={orientation}\r\n      className={cn(\r\n        \"shrink-0 bg-border\",\r\n        orientation === \"horizontal\" ? \"h-[1px] w-full\" : \"h-full w-[1px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n)\r\nSeparator.displayName = SeparatorPrimitive.Root.displayName\r\n\r\nexport { Separator }\r\n"], "names": [], "mappings": ";;;;;;;;AAAA;;;;;AAOA,MAAM,0BAAY,gNAAM,UAAU,CAIhC,CACE,EAAE,SAAS,EAAE,cAAc,YAAY,EAAE,aAAa,IAAI,EAAE,GAAG,OAAO,EACtE,oBAEA,wPAAC,sKAAmB,IAAI;QACtB,KAAK;QACL,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,sBACA,gBAAgB,eAAe,mBAAmB,kBAClD;QAED,GAAG,KAAK;;;;;;AAIf,UAAU,WAAW,GAAG,sKAAmB,IAAI,CAAC,WAAW"}}, {"offset": {"line": 216, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 221, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/components/auth.tsx"], "sourcesContent": ["import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'\r\nimport { Button } from '@/components/ui/button'\r\nimport { Input } from '@/components/ui/input'\r\nimport { Label } from '@/components/ui/label'\r\nimport { Separator } from '@/components/ui/separator'\r\nimport { cn } from '@/lib/utils'\r\nimport { Provider, SupabaseClient } from '@supabase/supabase-js'\r\nimport {\r\n  AlertCircle,\r\n  CheckCircle2,\r\n  KeyRound,\r\n  Loader2,\r\n  Mail,\r\n} from 'lucide-react'\r\nimport React, { useCallback, useEffect, useState } from 'react'\r\nimport * as SimpleIcons from 'simple-icons'\r\n\r\nconst VIEWS = {\r\n  SIGN_IN: 'sign_in',\r\n  SIGN_UP: 'sign_up',\r\n  FORGOTTEN_PASSWORD: 'forgotten_password',\r\n  MAGIC_LINK: 'magic_link',\r\n  UPDATE_PASSWORD: 'update_password',\r\n} as const\r\n\r\nexport type ViewType = (typeof VIEWS)[keyof typeof VIEWS]\r\n\r\ntype RedirectTo = undefined | string\r\n\r\nexport interface AuthProps {\r\n  supabaseClient: SupabaseClient\r\n  socialLayout?: 'horizontal' | 'vertical'\r\n  providers?: Provider[]\r\n  view?: ViewType\r\n  redirectTo?: RedirectTo\r\n  onlyThirdPartyProviders?: boolean\r\n  magicLink?: boolean\r\n  onSignUpValidate?: (email: string, password: string) => Promise<boolean>\r\n  metadata?: Record<string, any>\r\n}\r\n\r\ninterface SubComponentProps {\r\n  supabaseClient: SupabaseClient\r\n  setAuthView: (view: ViewType) => void\r\n  setLoading: (loading: boolean) => void\r\n  setError: (error: string | null) => void\r\n  setMessage: (message: string | null) => void\r\n  clearMessages: () => void\r\n  loading: boolean\r\n  redirectTo?: RedirectTo\r\n}\r\n\r\ninterface SocialAuthProps {\r\n  supabaseClient: SupabaseClient\r\n  providers: Provider[]\r\n  layout?: 'horizontal' | 'vertical'\r\n  redirectTo?: RedirectTo\r\n  setLoading: (loading: boolean) => void\r\n  setError: (error: string) => void\r\n  clearMessages: () => void\r\n  loading: boolean\r\n}\r\n\r\ninterface EmailAuthProps extends SubComponentProps {\r\n  view: typeof VIEWS.SIGN_IN | typeof VIEWS.SIGN_UP\r\n  magicLink?: boolean\r\n  onSignUpValidate?: (email: string, password: string) => Promise<boolean>\r\n  metadata?: Record<string, any>\r\n}\r\n\r\ninterface UseAuthFormReturn {\r\n  loading: boolean\r\n  error: string | null\r\n  message: string | null\r\n  setLoading: (loading: boolean) => void\r\n  setError: (error: string | null) => void\r\n  setMessage: (message: string | null) => void\r\n  clearMessages: () => void\r\n}\r\n\r\nconst ProviderIcons: {\r\n  [key in Provider]?: React.ComponentType<{ className?: string }>\r\n} = {\r\n  github: ({ className }) => (\r\n    <svg\r\n      role=\"img\"\r\n      viewBox=\"0 0 24 24\"\r\n      className={className}\r\n      fill=\"currentColor\"\r\n      dangerouslySetInnerHTML={{ __html: SimpleIcons.siGithub.svg }}\r\n    />\r\n  ),\r\n  google: ({ className }) => (\r\n    <svg\r\n      role=\"img\"\r\n      viewBox=\"0 0 24 24\"\r\n      className={className}\r\n      fill=\"currentColor\"\r\n      dangerouslySetInnerHTML={{ __html: SimpleIcons.siGoogle.svg }}\r\n    />\r\n  ),\r\n}\r\n\r\nfunction useAuthForm(): UseAuthFormReturn {\r\n  const [loading, setLoading] = useState(false)\r\n  const [error, setErrorState] = useState<string | null>(null)\r\n  const [message, setMessageState] = useState<string | null>(null)\r\n\r\n  const setError = useCallback((errorMsg: string | null) => {\r\n    setErrorState(errorMsg)\r\n    if (errorMsg) setMessageState(null)\r\n  }, [])\r\n\r\n  const setMessage = useCallback((msg: string | null) => {\r\n    setMessageState(msg)\r\n    if (msg) setErrorState(null)\r\n  }, [])\r\n\r\n  const clearMessages = useCallback(() => {\r\n    setErrorState(null)\r\n    setMessageState(null)\r\n  }, [])\r\n\r\n  return {\r\n    loading,\r\n    error,\r\n    message,\r\n    setLoading,\r\n    setError,\r\n    setMessage,\r\n    clearMessages,\r\n  }\r\n}\r\n\r\nfunction SocialAuth({\r\n  supabaseClient,\r\n  providers,\r\n  layout = 'vertical',\r\n  redirectTo,\r\n  setLoading,\r\n  setError,\r\n  clearMessages,\r\n  loading,\r\n}: SocialAuthProps) {\r\n  const handleProviderSignIn = async (provider: Provider) => {\r\n    clearMessages()\r\n    setLoading(true)\r\n    const { error } = await supabaseClient.auth.signInWithOAuth({\r\n      provider,\r\n      options: { redirectTo },\r\n    })\r\n    if (error) setError(error.message)\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className={cn(\r\n        'space-y-3',\r\n        layout === 'horizontal' && 'flex space-y-0 space-x-3',\r\n      )}\r\n    >\r\n      {providers.map((provider) => {\r\n        const IconComponent = ProviderIcons[provider]\r\n        const providerName =\r\n          provider.charAt(0).toUpperCase() + provider.slice(1)\r\n        return (\r\n          <Button\r\n            key={provider}\r\n            variant=\"outline\"\r\n            className=\"w-full flex items-center justify-center gap-2\"\r\n            onClick={() => handleProviderSignIn(provider)}\r\n            disabled={loading}\r\n          >\r\n            {IconComponent && <IconComponent className=\"h-4 w-4\" />}\r\n            {layout === 'vertical'\r\n              ? `Continue with ${providerName}`\r\n              : providerName}\r\n          </Button>\r\n        )\r\n      })}\r\n    </div>\r\n  )\r\n}\r\n\r\ninterface SignInFormProps extends SubComponentProps {\r\n  magicLink?: boolean\r\n}\r\n\r\nfunction SignInForm({\r\n  supabaseClient,\r\n  setAuthView,\r\n  setLoading,\r\n  setError,\r\n  clearMessages,\r\n  loading,\r\n}: SignInFormProps) {\r\n  const [email, setEmail] = useState('')\r\n  const [password, setPassword] = useState('')\r\n\r\n  const handleSignIn = async (e: React.FormEvent<HTMLFormElement>) => {\r\n    e.preventDefault()\r\n    clearMessages()\r\n    setLoading(true)\r\n\r\n    try {\r\n      const { error } = await supabaseClient.auth.signInWithPassword({\r\n        email,\r\n        password,\r\n      })\r\n      if (error) throw error\r\n    } catch (error: any) {\r\n      setError(error.message || 'An unexpected error occurred.')\r\n    } finally {\r\n      setLoading(false)\r\n    }\r\n  }\r\n\r\n  return (\r\n    <form id=\"auth-sign-in\" onSubmit={handleSignIn} className=\"space-y-4\">\r\n      <div className=\"space-y-2\">\r\n        <Label htmlFor=\"email\">Email address</Label>\r\n        <div className=\"relative\">\r\n          <Mail className=\"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground\" />\r\n          <Input\r\n            id=\"email\"\r\n            type=\"email\"\r\n            placeholder=\"<EMAIL>\"\r\n            value={email}\r\n            onChange={(e) => setEmail(e.target.value)}\r\n            required\r\n            className=\"pl-10\"\r\n            autoComplete=\"email\"\r\n          />\r\n        </div>\r\n      </div>\r\n      <div className=\"space-y-2\">\r\n        <div className=\"flex items-center justify-between\">\r\n          <Label htmlFor=\"password\">Password</Label>\r\n          <Button\r\n            variant=\"link\"\r\n            type=\"button\"\r\n            onClick={() => setAuthView(VIEWS.FORGOTTEN_PASSWORD)}\r\n            className=\"p-0 h-auto font-normal text-muted-foreground text-sm\"\r\n          >\r\n            Forgot your password?\r\n          </Button>\r\n        </div>\r\n        <div className=\"relative\">\r\n          <KeyRound className=\"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground\" />\r\n          <Input\r\n            id=\"password\"\r\n            type=\"password\"\r\n            placeholder=\"••••••••\"\r\n            value={password}\r\n            onChange={(e) => setPassword(e.target.value)}\r\n            required\r\n            className=\"pl-10\"\r\n            autoComplete=\"current-password\"\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      <Button type=\"submit\" className=\"w-full\" disabled={loading}>\r\n        {loading && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\r\n        Sign In\r\n      </Button>\r\n    </form>\r\n  )\r\n}\r\n\r\ninterface SignUpFormProps extends SubComponentProps {\r\n  onSignUpValidate?: (email: string, password: string) => Promise<boolean>\r\n  metadata?: Record<string, any>\r\n}\r\n\r\nfunction SignUpForm({\r\n  supabaseClient,\r\n  setAuthView,\r\n  setLoading,\r\n  setError,\r\n  setMessage,\r\n  clearMessages,\r\n  loading,\r\n  redirectTo,\r\n  onSignUpValidate,\r\n  metadata,\r\n}: SignUpFormProps) {\r\n  const [email, setEmail] = useState('')\r\n  const [password, setPassword] = useState('')\r\n  const [confirmPassword, setConfirmPassword] = useState('')\r\n\r\n  const handleSignUp = async (e: React.FormEvent<HTMLFormElement>) => {\r\n    e.preventDefault()\r\n    clearMessages()\r\n    setLoading(true)\r\n\r\n    try {\r\n      if (password !== confirmPassword) {\r\n        throw new Error('Passwords do not match')\r\n      }\r\n      if (onSignUpValidate) {\r\n        const isValid = await onSignUpValidate(email, password)\r\n        if (!isValid) {\r\n          throw new Error(\r\n            'Invalid email address. Please use a different email.',\r\n          )\r\n        }\r\n      }\r\n      const { data, error } = await supabaseClient.auth.signUp({\r\n        email,\r\n        password,\r\n        options: {\r\n          emailRedirectTo: redirectTo,\r\n          data: metadata,\r\n        },\r\n      })\r\n      if (error) throw error\r\n      if (data.user && !data.session) {\r\n        setMessage('Check your email for the confirmation link.')\r\n      }\r\n    } catch (error: any) {\r\n      setError(error.message || 'An unexpected error occurred.')\r\n    } finally {\r\n      setLoading(false)\r\n    }\r\n  }\r\n\r\n  return (\r\n    <form id=\"auth-sign-up\" onSubmit={handleSignUp} className=\"space-y-4\">\r\n      <div className=\"space-y-2\">\r\n        <Label htmlFor=\"email\">Email address</Label>\r\n        <div className=\"relative\">\r\n          <Mail className=\"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground\" />\r\n          <Input\r\n            id=\"email\"\r\n            type=\"email\"\r\n            placeholder=\"<EMAIL>\"\r\n            value={email}\r\n            onChange={(e) => setEmail(e.target.value)}\r\n            required\r\n            className=\"pl-10\"\r\n            autoComplete=\"email\"\r\n          />\r\n        </div>\r\n      </div>\r\n      <div className=\"space-y-2\">\r\n        <Label htmlFor=\"password\">Password</Label>\r\n        <div className=\"relative\">\r\n          <KeyRound className=\"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground\" />\r\n          <Input\r\n            id=\"password\"\r\n            type=\"password\"\r\n            placeholder=\"••••••••\"\r\n            value={password}\r\n            onChange={(e) => setPassword(e.target.value)}\r\n            required\r\n            className=\"pl-10\"\r\n            autoComplete=\"new-password\"\r\n          />\r\n        </div>\r\n      </div>\r\n      <div className=\"space-y-2\">\r\n        <Label htmlFor=\"confirm-password\">Confirm Password</Label>\r\n        <div className=\"relative\">\r\n          <KeyRound className=\"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground\" />\r\n          <Input\r\n            id=\"confirm-password\"\r\n            type=\"password\"\r\n            placeholder=\"••••••••\"\r\n            value={confirmPassword}\r\n            onChange={(e) => setConfirmPassword(e.target.value)}\r\n            required\r\n            className=\"pl-10\"\r\n            autoComplete=\"new-password\"\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      <Button type=\"submit\" className=\"w-full\" disabled={loading}>\r\n        {loading && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\r\n        Sign Up\r\n      </Button>\r\n    </form>\r\n  )\r\n}\r\n\r\nfunction MagicLink({\r\n  supabaseClient,\r\n  setAuthView,\r\n  setLoading,\r\n  setError,\r\n  setMessage,\r\n  clearMessages,\r\n  loading,\r\n  redirectTo,\r\n}: SubComponentProps) {\r\n  const [email, setEmail] = useState('')\r\n\r\n  const handleMagicLinkSignIn = async (e: React.FormEvent<HTMLFormElement>) => {\r\n    e.preventDefault()\r\n    clearMessages()\r\n    setLoading(true)\r\n    const { error } = await supabaseClient.auth.signInWithOtp({\r\n      email,\r\n      options: {\r\n        emailRedirectTo: redirectTo,\r\n      },\r\n    })\r\n    if (error) setError(error.message)\r\n    else setMessage('Check your email for the magic link.')\r\n    setLoading(false)\r\n  }\r\n\r\n  return (\r\n    <form\r\n      id=\"auth-magic-link\"\r\n      onSubmit={handleMagicLinkSignIn}\r\n      className=\"space-y-4\"\r\n    >\r\n      <div className=\"space-y-2\">\r\n        <Label htmlFor=\"email\">Email address</Label>\r\n        <div className=\"relative\">\r\n          <Mail className=\"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground\" />\r\n          <Input\r\n            id=\"email\"\r\n            type=\"email\"\r\n            placeholder=\"<EMAIL>\"\r\n            value={email}\r\n            onChange={(e) => setEmail(e.target.value)}\r\n            required\r\n            className=\"pl-10\"\r\n            autoComplete=\"email\"\r\n          />\r\n        </div>\r\n      </div>\r\n      <Button type=\"submit\" className=\"w-full\" disabled={loading}>\r\n        {loading && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\r\n        Send Magic Link\r\n      </Button>\r\n    </form>\r\n  )\r\n}\r\n\r\nfunction ForgottenPassword({\r\n  supabaseClient,\r\n  setLoading,\r\n  setError,\r\n  setMessage,\r\n  clearMessages,\r\n  loading,\r\n  redirectTo,\r\n}: Omit<SubComponentProps, 'email' | 'setEmail'>) {\r\n  const [email, setEmail] = useState('')\r\n\r\n  const handlePasswordReset = async (e: React.FormEvent<HTMLFormElement>) => {\r\n    e.preventDefault()\r\n    clearMessages()\r\n    setLoading(true)\r\n    const { error } = await supabaseClient.auth.resetPasswordForEmail(email, {\r\n      redirectTo,\r\n    })\r\n    if (error) setError(error.message)\r\n    else setMessage('Check your email for password reset instructions.')\r\n    setLoading(false)\r\n  }\r\n\r\n  return (\r\n    <form\r\n      id=\"auth-forgot-password\"\r\n      onSubmit={handlePasswordReset}\r\n      className=\"space-y-4\"\r\n    >\r\n      <div className=\"space-y-2\">\r\n        <Label htmlFor=\"email\">Email address</Label>\r\n        <div className=\"relative\">\r\n          <Mail className=\"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground\" />\r\n          <Input\r\n            id=\"email\"\r\n            type=\"email\"\r\n            placeholder=\"<EMAIL>\"\r\n            value={email}\r\n            onChange={(e) => setEmail(e.target.value)}\r\n            required\r\n            className=\"pl-10\"\r\n            autoComplete=\"email\"\r\n          />\r\n        </div>\r\n      </div>\r\n      <Button type=\"submit\" className=\"w-full\" disabled={loading}>\r\n        {loading && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\r\n        Send Reset Instructions\r\n      </Button>\r\n    </form>\r\n  )\r\n}\r\n\r\nfunction UpdatePassword({\r\n  supabaseClient,\r\n  setLoading,\r\n  setError,\r\n  setMessage,\r\n  clearMessages,\r\n  loading,\r\n}: Omit<\r\n  SubComponentProps,\r\n  'setAuthView' | 'redirectTo' | 'email' | 'setEmail'\r\n>) {\r\n  const [password, setPassword] = useState('')\r\n\r\n  const handlePasswordUpdate = async (e: React.FormEvent<HTMLFormElement>) => {\r\n    e.preventDefault()\r\n    clearMessages()\r\n    setLoading(true)\r\n    const { error } = await supabaseClient.auth.updateUser({ password })\r\n    if (error) setError(error.message)\r\n    else setMessage('Password updated successfully.')\r\n    setLoading(false)\r\n    if (!error) setPassword('')\r\n  }\r\n\r\n  return (\r\n    <form\r\n      id=\"auth-update-password\"\r\n      onSubmit={handlePasswordUpdate}\r\n      className=\"space-y-4\"\r\n    >\r\n      <h3 className=\"text-lg font-semibold\">Update Password</h3>\r\n      <div className=\"space-y-2\">\r\n        <Label htmlFor=\"new-password\">New Password</Label>\r\n        <div className=\"relative\">\r\n          <KeyRound className=\"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground\" />\r\n          <Input\r\n            id=\"new-password\"\r\n            type=\"password\"\r\n            placeholder=\"Enter new password\"\r\n            value={password}\r\n            onChange={(e) => setPassword(e.target.value)}\r\n            required\r\n            className=\"pl-10\"\r\n            autoComplete=\"new-password\"\r\n          />\r\n        </div>\r\n      </div>\r\n      <Button type=\"submit\" className=\"w-full\" disabled={loading}>\r\n        {loading && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\r\n        Update Password\r\n      </Button>\r\n    </form>\r\n  )\r\n}\r\n\r\nfunction Auth({\r\n  supabaseClient,\r\n  socialLayout = 'vertical',\r\n  providers,\r\n  view = VIEWS.SIGN_IN,\r\n  redirectTo,\r\n  onlyThirdPartyProviders = false,\r\n  magicLink = false,\r\n  onSignUpValidate,\r\n  metadata,\r\n}: AuthProps): JSX.Element | null {\r\n  const [authView, setAuthView] = useState<ViewType>(view)\r\n  const {\r\n    loading,\r\n    error,\r\n    message,\r\n    setLoading,\r\n    setError,\r\n    setMessage,\r\n    clearMessages,\r\n  } = useAuthForm()\r\n\r\n  useEffect(() => {\r\n    setAuthView(view)\r\n    setError(null)\r\n    setMessage(null)\r\n  }, [view, setError, setMessage])\r\n\r\n  const setAuthViewAndClearMessages = useCallback(\r\n    (newView: ViewType) => {\r\n      setAuthView(newView)\r\n      setError(null)\r\n      setMessage(null)\r\n    },\r\n    [setError, setMessage],\r\n  )\r\n\r\n  const commonProps = {\r\n    supabaseClient,\r\n    setAuthView: setAuthViewAndClearMessages,\r\n    setLoading,\r\n    setError,\r\n    setMessage,\r\n    clearMessages,\r\n    loading,\r\n    redirectTo,\r\n  }\r\n\r\n  let viewComponent: React.ReactNode = null\r\n\r\n  switch (authView) {\r\n    case VIEWS.SIGN_IN:\r\n      viewComponent = <SignInForm {...commonProps} />\r\n      break\r\n    case VIEWS.SIGN_UP:\r\n      viewComponent = (\r\n        <SignUpForm\r\n          {...commonProps}\r\n          onSignUpValidate={onSignUpValidate}\r\n          metadata={metadata}\r\n        />\r\n      )\r\n      break\r\n    case VIEWS.FORGOTTEN_PASSWORD:\r\n      viewComponent = <ForgottenPassword {...commonProps} />\r\n      break\r\n    case VIEWS.MAGIC_LINK:\r\n      viewComponent = <MagicLink {...commonProps} />\r\n      break\r\n    case VIEWS.UPDATE_PASSWORD:\r\n      viewComponent = <UpdatePassword {...commonProps} />\r\n      break\r\n    default:\r\n      viewComponent = null\r\n  }\r\n\r\n  const showSocialAuth = providers && providers.length > 0\r\n  const showSeparator = showSocialAuth && !onlyThirdPartyProviders\r\n\r\n  return (\r\n    <div className=\"w-full space-y-4\">\r\n      {authView === VIEWS.UPDATE_PASSWORD ? (\r\n        viewComponent\r\n      ) : (\r\n        <>\r\n          {showSocialAuth && (\r\n            <SocialAuth\r\n              supabaseClient={supabaseClient}\r\n              providers={providers || []}\r\n              layout={socialLayout}\r\n              redirectTo={redirectTo}\r\n              setLoading={setLoading}\r\n              setError={setError}\r\n              clearMessages={clearMessages}\r\n              loading={loading}\r\n            />\r\n          )}\r\n          {showSeparator && (\r\n            <div className=\"relative my-4\">\r\n              <div className=\"absolute inset-0 flex items-center\">\r\n                <Separator />\r\n              </div>\r\n              <div className=\"relative flex justify-center text-xs uppercase\">\r\n                <span className=\"bg-background px-2 text-muted-foreground\">\r\n                  Or continue with\r\n                </span>\r\n              </div>\r\n            </div>\r\n          )}\r\n          {!onlyThirdPartyProviders && viewComponent}\r\n        </>\r\n      )}\r\n\r\n      {!onlyThirdPartyProviders && authView !== VIEWS.UPDATE_PASSWORD && (\r\n        <div className=\"text-center text-sm space-y-1 mt-4\">\r\n          {authView === VIEWS.SIGN_IN && (\r\n            <>\r\n              {magicLink && (\r\n                <Button\r\n                  variant=\"link\"\r\n                  type=\"button\"\r\n                  onClick={() => setAuthViewAndClearMessages(VIEWS.MAGIC_LINK)}\r\n                  className=\"p-0 h-auto font-normal\"\r\n                >\r\n                  Sign in with magic link\r\n                </Button>\r\n              )}\r\n              <p className=\"text-muted-foreground\">\r\n                Don&apos;t have an account?{' '}\r\n                <Button\r\n                  variant=\"link\"\r\n                  type=\"button\"\r\n                  onClick={() => setAuthViewAndClearMessages(VIEWS.SIGN_UP)}\r\n                  className=\"p-0 h-auto underline\"\r\n                >\r\n                  Sign up\r\n                </Button>\r\n              </p>\r\n            </>\r\n          )}\r\n          {authView === VIEWS.SIGN_UP && (\r\n            <p className=\"text-muted-foreground\">\r\n              Already have an account?{' '}\r\n              <Button\r\n                variant=\"link\"\r\n                type=\"button\"\r\n                onClick={() => setAuthViewAndClearMessages(VIEWS.SIGN_IN)}\r\n                className=\"p-0 h-auto underline\"\r\n              >\r\n                Sign in\r\n              </Button>\r\n            </p>\r\n          )}\r\n          {authView === VIEWS.MAGIC_LINK && (\r\n            <Button\r\n              variant=\"link\"\r\n              type=\"button\"\r\n              onClick={() => setAuthViewAndClearMessages(VIEWS.SIGN_IN)}\r\n              className=\"p-0 h-auto font-normal\"\r\n            >\r\n              Sign in with password instead\r\n            </Button>\r\n          )}\r\n          {authView === VIEWS.FORGOTTEN_PASSWORD && (\r\n            <Button\r\n              variant=\"link\"\r\n              type=\"button\"\r\n              onClick={() => setAuthViewAndClearMessages(VIEWS.SIGN_IN)}\r\n              className=\"p-0 h-auto underline\"\r\n            >\r\n              Back to Sign In\r\n            </Button>\r\n          )}\r\n        </div>\r\n      )}\r\n\r\n      <div className=\"mt-4 space-y-2\">\r\n        {error && (\r\n          <Alert variant=\"destructive\">\r\n            <AlertCircle className=\"h-4 w-4\" />\r\n            <AlertTitle>Error</AlertTitle>\r\n            <AlertDescription>{error}</AlertDescription>\r\n          </Alert>\r\n        )}\r\n        {message && (\r\n          <Alert variant=\"default\">\r\n            <CheckCircle2 className=\"h-4 w-4\" />\r\n            <AlertTitle>Success</AlertTitle>\r\n            <AlertDescription>{message}</AlertDescription>\r\n          </Alert>\r\n        )}\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default Auth\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiBA,MAAM,QAAQ;IACZ,SAAS;IACT,SAAS;IACT,oBAAoB;IACpB,YAAY;IACZ,iBAAiB;AACnB;AAyDA,MAAM,gBAEF;IACF,QAAQ,CAAC,EAAE,SAAS,EAAE,iBACpB,wPAAC;YACC,MAAK;YACL,SAAQ;YACR,WAAW;YACX,MAAK;YACL,yBAAyB;gBAAE,QAAQ,0IAAY,QAAQ,CAAC,GAAG;YAAC;;;;;;IAGhE,QAAQ,CAAC,EAAE,SAAS,EAAE,iBACpB,wPAAC;YACC,MAAK;YACL,SAAQ;YACR,WAAW;YACX,MAAK;YACL,yBAAyB;gBAAE,QAAQ,0IAAY,QAAQ,CAAC,GAAG;YAAC;;;;;;AAGlE;AAEA,SAAS;IACP,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,cAAc,GAAG,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAiB;IACvD,MAAM,CAAC,SAAS,gBAAgB,GAAG,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAiB;IAE3D,MAAM,WAAW,CAAA,GAAA,+MAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC5B,cAAc;QACd,IAAI,UAAU,gBAAgB;IAChC,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,+MAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC9B,gBAAgB;QAChB,IAAI,KAAK,cAAc;IACzB,GAAG,EAAE;IAEL,MAAM,gBAAgB,CAAA,GAAA,+MAAA,CAAA,cAAW,AAAD,EAAE;QAChC,cAAc;QACd,gBAAgB;IAClB,GAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;AAEA,SAAS,WAAW,EAClB,cAAc,EACd,SAAS,EACT,SAAS,UAAU,EACnB,UAAU,EACV,UAAU,EACV,QAAQ,EACR,aAAa,EACb,OAAO,EACS;IAChB,MAAM,uBAAuB,OAAO;QAClC;QACA,WAAW;QACX,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,eAAe,IAAI,CAAC,eAAe,CAAC;YAC1D;YACA,SAAS;gBAAE;YAAW;QACxB;QACA,IAAI,OAAO,SAAS,MAAM,OAAO;IACnC;IAEA,qBACE,wPAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,aACA,WAAW,gBAAgB;kBAG5B,UAAU,GAAG,CAAC,CAAC;YACd,MAAM,gBAAgB,aAAa,CAAC,SAAS;YAC7C,MAAM,eACJ,SAAS,MAAM,CAAC,GAAG,WAAW,KAAK,SAAS,KAAK,CAAC;YACpD,qBACE,wPAAC,2HAAA,CAAA,SAAM;gBAEL,SAAQ;gBACR,WAAU;gBACV,SAAS,IAAM,qBAAqB;gBACpC,UAAU;;oBAET,+BAAiB,wPAAC;wBAAc,WAAU;;;;;;oBAC1C,WAAW,aACR,CAAC,cAAc,EAAE,aAAa,CAAC,GAC/B;;eATC;;;;;QAYX;;;;;;AAGN;AAMA,SAAS,WAAW,EAClB,cAAc,EACd,WAAW,EACX,UAAU,EACV,QAAQ,EACR,aAAa,EACb,OAAO,EACS;IAChB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB;QACA,WAAW;QAEX,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,eAAe,IAAI,CAAC,kBAAkB,CAAC;gBAC7D;gBACA;YACF;YACA,IAAI,OAAO,MAAM;QACnB,EAAE,OAAO,OAAY;YACnB,SAAS,MAAM,OAAO,IAAI;QAC5B,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,wPAAC;QAAK,IAAG;QAAe,UAAU;QAAc,WAAU;;0BACxD,wPAAC;gBAAI,WAAU;;kCACb,wPAAC,0HAAA,CAAA,QAAK;wBAAC,SAAQ;kCAAQ;;;;;;kCACvB,wPAAC;wBAAI,WAAU;;0CACb,wPAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,wPAAC,0HAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,MAAK;gCACL,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gCACxC,QAAQ;gCACR,WAAU;gCACV,cAAa;;;;;;;;;;;;;;;;;;0BAInB,wPAAC;gBAAI,WAAU;;kCACb,wPAAC;wBAAI,WAAU;;0CACb,wPAAC,0HAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAW;;;;;;0CAC1B,wPAAC,2HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,YAAY,MAAM,kBAAkB;gCACnD,WAAU;0CACX;;;;;;;;;;;;kCAIH,wPAAC;wBAAI,WAAU;;0CACb,wPAAC,8MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,wPAAC,0HAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,MAAK;gCACL,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;gCAC3C,QAAQ;gCACR,WAAU;gCACV,cAAa;;;;;;;;;;;;;;;;;;0BAKnB,wPAAC,2HAAA,CAAA,SAAM;gBAAC,MAAK;gBAAS,WAAU;gBAAS,UAAU;;oBAChD,yBAAW,wPAAC,iNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;oBAA+B;;;;;;;;;;;;;AAKtE;AAOA,SAAS,WAAW,EAClB,cAAc,EACd,WAAW,EACX,UAAU,EACV,QAAQ,EACR,UAAU,EACV,aAAa,EACb,OAAO,EACP,UAAU,EACV,gBAAgB,EAChB,QAAQ,EACQ;IAChB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB;QACA,WAAW;QAEX,IAAI;YACF,IAAI,aAAa,iBAAiB;gBAChC,MAAM,IAAI,MAAM;YAClB;YACA,IAAI,kBAAkB;gBACpB,MAAM,UAAU,MAAM,iBAAiB,OAAO;gBAC9C,IAAI,CAAC,SAAS;oBACZ,MAAM,IAAI,MACR;gBAEJ;YACF;YACA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,eAAe,IAAI,CAAC,MAAM,CAAC;gBACvD;gBACA;gBACA,SAAS;oBACP,iBAAiB;oBACjB,MAAM;gBACR;YACF;YACA,IAAI,OAAO,MAAM;YACjB,IAAI,KAAK,IAAI,IAAI,CAAC,KAAK,OAAO,EAAE;gBAC9B,WAAW;YACb;QACF,EAAE,OAAO,OAAY;YACnB,SAAS,MAAM,OAAO,IAAI;QAC5B,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,wPAAC;QAAK,IAAG;QAAe,UAAU;QAAc,WAAU;;0BACxD,wPAAC;gBAAI,WAAU;;kCACb,wPAAC,0HAAA,CAAA,QAAK;wBAAC,SAAQ;kCAAQ;;;;;;kCACvB,wPAAC;wBAAI,WAAU;;0CACb,wPAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,wPAAC,0HAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,MAAK;gCACL,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gCACxC,QAAQ;gCACR,WAAU;gCACV,cAAa;;;;;;;;;;;;;;;;;;0BAInB,wPAAC;gBAAI,WAAU;;kCACb,wPAAC,0HAAA,CAAA,QAAK;wBAAC,SAAQ;kCAAW;;;;;;kCAC1B,wPAAC;wBAAI,WAAU;;0CACb,wPAAC,8MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,wPAAC,0HAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,MAAK;gCACL,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;gCAC3C,QAAQ;gCACR,WAAU;gCACV,cAAa;;;;;;;;;;;;;;;;;;0BAInB,wPAAC;gBAAI,WAAU;;kCACb,wPAAC,0HAAA,CAAA,QAAK;wBAAC,SAAQ;kCAAmB;;;;;;kCAClC,wPAAC;wBAAI,WAAU;;0CACb,wPAAC,8MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,wPAAC,0HAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,MAAK;gCACL,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;gCAClD,QAAQ;gCACR,WAAU;gCACV,cAAa;;;;;;;;;;;;;;;;;;0BAKnB,wPAAC,2HAAA,CAAA,SAAM;gBAAC,MAAK;gBAAS,WAAU;gBAAS,UAAU;;oBAChD,yBAAW,wPAAC,iNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;oBAA+B;;;;;;;;;;;;;AAKtE;AAEA,SAAS,UAAU,EACjB,cAAc,EACd,WAAW,EACX,UAAU,EACV,QAAQ,EACR,UAAU,EACV,aAAa,EACb,OAAO,EACP,UAAU,EACQ;IAClB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,wBAAwB,OAAO;QACnC,EAAE,cAAc;QAChB;QACA,WAAW;QACX,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,eAAe,IAAI,CAAC,aAAa,CAAC;YACxD;YACA,SAAS;gBACP,iBAAiB;YACnB;QACF;QACA,IAAI,OAAO,SAAS,MAAM,OAAO;aAC5B,WAAW;QAChB,WAAW;IACb;IAEA,qBACE,wPAAC;QACC,IAAG;QACH,UAAU;QACV,WAAU;;0BAEV,wPAAC;gBAAI,WAAU;;kCACb,wPAAC,0HAAA,CAAA,QAAK;wBAAC,SAAQ;kCAAQ;;;;;;kCACvB,wPAAC;wBAAI,WAAU;;0CACb,wPAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,wPAAC,0HAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,MAAK;gCACL,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gCACxC,QAAQ;gCACR,WAAU;gCACV,cAAa;;;;;;;;;;;;;;;;;;0BAInB,wPAAC,2HAAA,CAAA,SAAM;gBAAC,MAAK;gBAAS,WAAU;gBAAS,UAAU;;oBAChD,yBAAW,wPAAC,iNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;oBAA+B;;;;;;;;;;;;;AAKtE;AAEA,SAAS,kBAAkB,EACzB,cAAc,EACd,UAAU,EACV,QAAQ,EACR,UAAU,EACV,aAAa,EACb,OAAO,EACP,UAAU,EACoC;IAC9C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,sBAAsB,OAAO;QACjC,EAAE,cAAc;QAChB;QACA,WAAW;QACX,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,eAAe,IAAI,CAAC,qBAAqB,CAAC,OAAO;YACvE;QACF;QACA,IAAI,OAAO,SAAS,MAAM,OAAO;aAC5B,WAAW;QAChB,WAAW;IACb;IAEA,qBACE,wPAAC;QACC,IAAG;QACH,UAAU;QACV,WAAU;;0BAEV,wPAAC;gBAAI,WAAU;;kCACb,wPAAC,0HAAA,CAAA,QAAK;wBAAC,SAAQ;kCAAQ;;;;;;kCACvB,wPAAC;wBAAI,WAAU;;0CACb,wPAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,wPAAC,0HAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,MAAK;gCACL,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gCACxC,QAAQ;gCACR,WAAU;gCACV,cAAa;;;;;;;;;;;;;;;;;;0BAInB,wPAAC,2HAAA,CAAA,SAAM;gBAAC,MAAK;gBAAS,WAAU;gBAAS,UAAU;;oBAChD,yBAAW,wPAAC,iNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;oBAA+B;;;;;;;;;;;;;AAKtE;AAEA,SAAS,eAAe,EACtB,cAAc,EACd,UAAU,EACV,QAAQ,EACR,UAAU,EACV,aAAa,EACb,OAAO,EAIR;IACC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,uBAAuB,OAAO;QAClC,EAAE,cAAc;QAChB;QACA,WAAW;QACX,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,eAAe,IAAI,CAAC,UAAU,CAAC;YAAE;QAAS;QAClE,IAAI,OAAO,SAAS,MAAM,OAAO;aAC5B,WAAW;QAChB,WAAW;QACX,IAAI,CAAC,OAAO,YAAY;IAC1B;IAEA,qBACE,wPAAC;QACC,IAAG;QACH,UAAU;QACV,WAAU;;0BAEV,wPAAC;gBAAG,WAAU;0BAAwB;;;;;;0BACtC,wPAAC;gBAAI,WAAU;;kCACb,wPAAC,0HAAA,CAAA,QAAK;wBAAC,SAAQ;kCAAe;;;;;;kCAC9B,wPAAC;wBAAI,WAAU;;0CACb,wPAAC,8MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,wPAAC,0HAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,MAAK;gCACL,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;gCAC3C,QAAQ;gCACR,WAAU;gCACV,cAAa;;;;;;;;;;;;;;;;;;0BAInB,wPAAC,2HAAA,CAAA,SAAM;gBAAC,MAAK;gBAAS,WAAU;gBAAS,UAAU;;oBAChD,yBAAW,wPAAC,iNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;oBAA+B;;;;;;;;;;;;;AAKtE;AAEA,SAAS,KAAK,EACZ,cAAc,EACd,eAAe,UAAU,EACzB,SAAS,EACT,OAAO,MAAM,OAAO,EACpB,UAAU,EACV,0BAA0B,KAAK,EAC/B,YAAY,KAAK,EACjB,gBAAgB,EAChB,QAAQ,EACE;IACV,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAY;IACnD,MAAM,EACJ,OAAO,EACP,KAAK,EACL,OAAO,EACP,UAAU,EACV,QAAQ,EACR,UAAU,EACV,aAAa,EACd,GAAG;IAEJ,CAAA,GAAA,+MAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY;QACZ,SAAS;QACT,WAAW;IACb,GAAG;QAAC;QAAM;QAAU;KAAW;IAE/B,MAAM,8BAA8B,CAAA,GAAA,+MAAA,CAAA,cAAW,AAAD,EAC5C,CAAC;QACC,YAAY;QACZ,SAAS;QACT,WAAW;IACb,GACA;QAAC;QAAU;KAAW;IAGxB,MAAM,cAAc;QAClB;QACA,aAAa;QACb;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,IAAI,gBAAiC;IAErC,OAAQ;QACN,KAAK,MAAM,OAAO;YAChB,8BAAgB,wPAAC;gBAAY,GAAG,WAAW;;;;;;YAC3C;QACF,KAAK,MAAM,OAAO;YAChB,8BACE,wPAAC;gBACE,GAAG,WAAW;gBACf,kBAAkB;gBAClB,UAAU;;;;;;YAGd;QACF,KAAK,MAAM,kBAAkB;YAC3B,8BAAgB,wPAAC;gBAAmB,GAAG,WAAW;;;;;;YAClD;QACF,KAAK,MAAM,UAAU;YACnB,8BAAgB,wPAAC;gBAAW,GAAG,WAAW;;;;;;YAC1C;QACF,KAAK,MAAM,eAAe;YACxB,8BAAgB,wPAAC;gBAAgB,GAAG,WAAW;;;;;;YAC/C;QACF;YACE,gBAAgB;IACpB;IAEA,MAAM,iBAAiB,aAAa,UAAU,MAAM,GAAG;IACvD,MAAM,gBAAgB,kBAAkB,CAAC;IAEzC,qBACE,wPAAC;QAAI,WAAU;;YACZ,aAAa,MAAM,eAAe,GACjC,8BAEA;;oBACG,gCACC,wPAAC;wBACC,gBAAgB;wBAChB,WAAW,aAAa,EAAE;wBAC1B,QAAQ;wBACR,YAAY;wBACZ,YAAY;wBACZ,UAAU;wBACV,eAAe;wBACf,SAAS;;;;;;oBAGZ,+BACC,wPAAC;wBAAI,WAAU;;0CACb,wPAAC;gCAAI,WAAU;0CACb,cAAA,wPAAC,8HAAA,CAAA,YAAS;;;;;;;;;;0CAEZ,wPAAC;gCAAI,WAAU;0CACb,cAAA,wPAAC;oCAAK,WAAU;8CAA2C;;;;;;;;;;;;;;;;;oBAMhE,CAAC,2BAA2B;;;YAIhC,CAAC,2BAA2B,aAAa,MAAM,eAAe,kBAC7D,wPAAC;gBAAI,WAAU;;oBACZ,aAAa,MAAM,OAAO,kBACzB;;4BACG,2BACC,wPAAC,2HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,4BAA4B,MAAM,UAAU;gCAC3D,WAAU;0CACX;;;;;;0CAIH,wPAAC;gCAAE,WAAU;;oCAAwB;oCACP;kDAC5B,wPAAC,2HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,4BAA4B,MAAM,OAAO;wCACxD,WAAU;kDACX;;;;;;;;;;;;;;oBAMN,aAAa,MAAM,OAAO,kBACzB,wPAAC;wBAAE,WAAU;;4BAAwB;4BACV;0CACzB,wPAAC,2HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,4BAA4B,MAAM,OAAO;gCACxD,WAAU;0CACX;;;;;;;;;;;;oBAKJ,aAAa,MAAM,UAAU,kBAC5B,wPAAC,2HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,4BAA4B,MAAM,OAAO;wBACxD,WAAU;kCACX;;;;;;oBAIF,aAAa,MAAM,kBAAkB,kBACpC,wPAAC,2HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,4BAA4B,MAAM,OAAO;wBACxD,WAAU;kCACX;;;;;;;;;;;;0BAOP,wPAAC;gBAAI,WAAU;;oBACZ,uBACC,wPAAC,0HAAA,CAAA,QAAK;wBAAC,SAAQ;;0CACb,wPAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,wPAAC,0HAAA,CAAA,aAAU;0CAAC;;;;;;0CACZ,wPAAC,0HAAA,CAAA,mBAAgB;0CAAE;;;;;;;;;;;;oBAGtB,yBACC,wPAAC,0HAAA,CAAA,QAAK;wBAAC,SAAQ;;0CACb,wPAAC,qNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;0CACxB,wPAAC,0HAAA,CAAA,aAAU;0CAAC;;;;;;0CACZ,wPAAC,0HAAA,CAAA,mBAAgB;0CAAE;;;;;;;;;;;;;;;;;;;;;;;;AAM/B;uCAEe"}}, {"offset": {"line": 1320, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1325, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/components/logo.tsx"], "sourcesContent": ["import Image from 'next/image'\r\nimport { ComponentProps } from 'react'\r\n\r\ninterface LogoProps extends Omit<ComponentProps<typeof Image>, 'src' | 'alt' | 'width' | 'height'> {\r\n  width?: number\r\n  height?: number\r\n  style?: string // Custom style prop for logo variants\r\n}\r\n\r\nexport default function Logo({\r\n  width = 232,\r\n  height = 232,\r\n  style,\r\n  ...props\r\n}: LogoProps) {\r\n  // Filter out the custom style prop since Next.js Image doesn't accept string styles\r\n  const { style: _, ...imageProps } = { style, ...props }\r\n\r\n  return (\r\n    <Image\r\n      src=\"/Logo.png\"\r\n      alt=\"Fragments Logo\"\r\n      width={width}\r\n      height={height}\r\n      {...imageProps}\r\n    />\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;AASe,SAAS,KAAK,EAC3B,QAAQ,GAAG,EACX,SAAS,GAAG,EACZ,KAAK,EACL,GAAG,OACO;IACV,oFAAoF;IACpF,MAAM,EAAE,OAAO,CAAC,EAAE,GAAG,YAAY,GAAG;QAAE;QAAO,GAAG,KAAK;IAAC;IAEtD,qBACE,wPAAC,6HAAA,CAAA,UAAK;QACJ,KAAI;QACJ,KAAI;QACJ,OAAO;QACP,QAAQ;QACP,GAAG,UAAU;;;;;;AAGpB"}}, {"offset": {"line": 1351, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1356, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 1363, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1368, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\r\nimport { Cross2Icon } from \"@radix-ui/react-icons\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Dialog = DialogPrimitive.Root\r\n\r\nconst DialogTrigger = DialogPrimitive.Trigger\r\n\r\nconst DialogPortal = DialogPrimitive.Portal\r\n\r\nconst DialogClose = DialogPrimitive.Close\r\n\r\nconst DialogOverlay = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Overlay\r\n    ref={ref}\r\n    className={cn(\r\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\r\n\r\nconst DialogContent = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\r\n>(({ className, children, ...props }, ref) => (\r\n  <DialogPortal>\r\n    <DialogOverlay />\r\n    <DialogPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"h-full md:h-auto fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\r\n        <Cross2Icon className=\"h-4 w-4\" />\r\n        <span className=\"sr-only\">Close</span>\r\n      </DialogPrimitive.Close>\r\n    </DialogPrimitive.Content>\r\n  </DialogPortal>\r\n))\r\nDialogContent.displayName = DialogPrimitive.Content.displayName\r\n\r\nconst DialogHeader = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nDialogHeader.displayName = \"DialogHeader\"\r\n\r\nconst DialogFooter = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nDialogFooter.displayName = \"DialogFooter\"\r\n\r\nconst DialogTitle = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Title>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Title\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-lg font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDialogTitle.displayName = DialogPrimitive.Title.displayName\r\n\r\nconst DialogDescription = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Description>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nDialogDescription.displayName = DialogPrimitive.Description.displayName\r\n\r\nexport {\r\n  Dialog,\r\n  DialogPortal,\r\n  DialogOverlay,\r\n  DialogTrigger,\r\n  DialogClose,\r\n  DialogContent,\r\n  DialogHeader,\r\n  DialogFooter,\r\n  DialogTitle,\r\n  DialogDescription,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;;;;;;AAQA,MAAM,SAAS,mKAAgB,IAAI;AAEnC,MAAM,gBAAgB,mKAAgB,OAAO;AAE7C,MAAM,eAAe,mKAAgB,MAAM;AAE3C,MAAM,cAAc,mKAAgB,KAAK;AAEzC,MAAM,8BAAgB,gNAAM,UAAU,CAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,wPAAC,mKAAgB,OAAO;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;;;;;;AAGb,cAAc,WAAW,GAAG,mKAAgB,OAAO,CAAC,WAAW;AAE/D,MAAM,8BAAgB,gNAAM,UAAU,CAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,wPAAC;;0BACC,wPAAC;;;;;0BACD,wPAAC,mKAAgB,OAAO;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,ghBACA;gBAED,GAAG,KAAK;;oBAER;kCACD,wPAAC,mKAAgB,KAAK;wBAAC,WAAU;;0CAC/B,wPAAC,gLAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;0CACtB,wPAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,mKAAgB,OAAO,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,wPAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,wPAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,gNAAM,UAAU,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,wPAAC,mKAAgB,KAAK;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAgB,KAAK,CAAC,WAAW;AAE3D,MAAM,kCAAoB,gNAAM,UAAU,CAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,wPAAC,mKAAgB,WAAW;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,mKAAgB,WAAW,CAAC,WAAW"}}, {"offset": {"line": 1495, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1500, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/components/auth-dialog.tsx"], "sourcesContent": ["import Auth, { ViewType } from './auth'\r\nimport Logo from './logo'\r\nimport { validateEmail } from '@/app/actions/validate-email'\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogTitle,\r\n  DialogDescription,\r\n} from '@/components/ui/dialog'\r\nimport { VisuallyHidden } from '@radix-ui/react-visually-hidden'\r\nimport { SupabaseClient } from '@supabase/supabase-js'\r\n\r\nexport function AuthDialog({\r\n  open,\r\n  setOpen,\r\n  supabase,\r\n  view,\r\n}: {\r\n  open: boolean\r\n  setOpen: (open: boolean) => void\r\n  supabase: SupabaseClient\r\n  view: ViewType\r\n}) {\r\n  return (\r\n    <Dialog open={open} onOpenChange={setOpen}>\r\n      <DialogContent>\r\n        <VisuallyHidden>\r\n          <DialogTitle>Sign in to Fragments</DialogTitle>\r\n          <DialogDescription>\r\n            Sign in or create an account to access Fragments\r\n          </DialogDescription>\r\n        </VisuallyHidden>\r\n        <div className=\"flex justify-center items-center flex-col\">\r\n          <h1 className=\"flex items-center gap-4 text-xl font-bold mb-6 w-full\">\r\n            <div className=\"flex items-center justify-center rounded-md shadow-md bg-black p-2\">\r\n              <Logo className=\"text-white w-6 h-6\" />\r\n            </div>\r\n            Sign in to Fragments\r\n          </h1>\r\n          <div className=\"w-full\">\r\n            <Auth\r\n              supabaseClient={supabase}\r\n              view={view}\r\n              providers={['github', 'google']}\r\n              socialLayout=\"horizontal\"\r\n              onSignUpValidate={validateEmail}\r\n              metadata={{\r\n                is_fragments_user: true,\r\n              }}\r\n            />\r\n          </div>\r\n        </div>\r\n      </DialogContent>\r\n    </Dialog>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAYO,SAAS,WAAW,EACzB,IAAI,EACJ,OAAO,EACP,QAAQ,EACR,IAAI,EAML;IACC,qBACE,wPAAC,2HAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,wPAAC,2HAAA,CAAA,gBAAa;;8BACZ,wPAAC,8KAAA,CAAA,iBAAc;;sCACb,wPAAC,2HAAA,CAAA,cAAW;sCAAC;;;;;;sCACb,wPAAC,2HAAA,CAAA,oBAAiB;sCAAC;;;;;;;;;;;;8BAIrB,wPAAC;oBAAI,WAAU;;sCACb,wPAAC;4BAAG,WAAU;;8CACZ,wPAAC;oCAAI,WAAU;8CACb,cAAA,wPAAC,mHAAA,CAAA,UAAI;wCAAC,WAAU;;;;;;;;;;;gCACZ;;;;;;;sCAGR,wPAAC;4BAAI,WAAU;sCACb,cAAA,wPAAC,mHAAA,CAAA,UAAI;gCACH,gBAAgB;gCAChB,MAAM;gCACN,WAAW;oCAAC;oCAAU;iCAAS;gCAC/B,cAAa;gCACb,kBAAkB,mIAAA,CAAA,gBAAa;gCAC/B,UAAU;oCACR,mBAAmB;gCACrB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOd"}}, {"offset": {"line": 1613, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1618, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/components/chat.tsx"], "sourcesContent": ["import { Message } from '@/lib/messages'\r\nimport { FragmentSchema } from '@/lib/schema'\r\nimport { ExecutionResult } from '@/lib/types'\r\nimport { DeepPartial } from 'ai'\r\nimport { LoaderIcon, Terminal } from 'lucide-react'\r\nimport { useEffect } from 'react'\r\n\r\nexport function Chat({\r\n  messages,\r\n  isLoading,\r\n  setCurrentPreview,\r\n}: {\r\n  messages: Message[]\r\n  isLoading: boolean\r\n  setCurrentPreview: (preview: {\r\n    fragment: DeepPartial<FragmentSchema> | undefined\r\n    result: ExecutionResult | undefined\r\n  }) => void\r\n}) {\r\n  useEffect(() => {\r\n    const chatContainer = document.getElementById('chat-container')\r\n    if (chatContainer) {\r\n      chatContainer.scrollTop = chatContainer.scrollHeight\r\n    }\r\n  }, [JSON.stringify(messages)])\r\n\r\n  return (\r\n    <div\r\n      id=\"chat-container\"\r\n      className=\"flex flex-col pb-12 gap-2 overflow-y-auto max-h-full\"\r\n    >\r\n      {messages.map((message: Message, index: number) => (\r\n        <div\r\n          className={`flex flex-col px-4 shadow-sm whitespace-pre-wrap ${message.role !== 'user' ? 'bg-accent dark:bg-white/5 border text-accent-foreground dark:text-muted-foreground py-4 rounded-2xl gap-4 w-full' : 'bg-gradient-to-b from-black/5 to-black/10 dark:from-black/30 dark:to-black/50 py-2 rounded-xl gap-2 w-fit'} font-serif`}\r\n          key={index}\r\n        >\r\n          {message.content.map((content, id) => {\r\n            if (content.type === 'text') {\r\n              return content.text\r\n            }\r\n            if (content.type === 'image') {\r\n              return (\r\n                <img\r\n                  key={id}\r\n                  src={content.image}\r\n                  alt=\"fragment\"\r\n                  className=\"mr-2 inline-block w-12 h-12 object-cover rounded-lg bg-white mb-2\"\r\n                />\r\n              )\r\n            }\r\n          })}\r\n          {message.object && (\r\n            <div\r\n              onClick={() =>\r\n                setCurrentPreview({\r\n                  fragment: message.object,\r\n                  result: message.result,\r\n                })\r\n              }\r\n              className=\"py-2 pl-2 w-full md:w-max flex items-center border rounded-xl select-none hover:bg-white dark:hover:bg-white/5 hover:cursor-pointer\"\r\n            >\r\n              <div className=\"rounded-[0.5rem] w-10 h-10 bg-black/5 dark:bg-white/5 self-stretch flex items-center justify-center\">\r\n                <Terminal strokeWidth={2} className=\"text-[#FF8800]\" />\r\n              </div>\r\n              <div className=\"pl-2 pr-4 flex flex-col\">\r\n                <span className=\"font-bold font-sans text-sm text-primary\">\r\n                  {message.object.title}\r\n                </span>\r\n                <span className=\"font-sans text-sm text-muted-foreground\">\r\n                  Click to see fragment\r\n                </span>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n      ))}\r\n      {isLoading && (\r\n        <div className=\"flex items-center gap-1 text-sm text-muted-foreground\">\r\n          <LoaderIcon strokeWidth={2} className=\"animate-spin w-4 h-4\" />\r\n          <span>Generating...</span>\r\n        </div>\r\n      )}\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAOO,SAAS,KAAK,EACnB,QAAQ,EACR,SAAS,EACT,iBAAiB,EAQlB;IACC,CAAA,GAAA,+MAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,SAAS,cAAc,CAAC;QAC9C,IAAI,eAAe;YACjB,cAAc,SAAS,GAAG,cAAc,YAAY;QACtD;IACF,GAAG;QAAC,KAAK,SAAS,CAAC;KAAU;IAE7B,qBACE,wPAAC;QACC,IAAG;QACH,WAAU;;YAET,SAAS,GAAG,CAAC,CAAC,SAAkB,sBAC/B,wPAAC;oBACC,WAAW,CAAC,iDAAiD,EAAE,QAAQ,IAAI,KAAK,SAAS,qHAAqH,4GAA4G,WAAW,CAAC;;wBAGrU,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,SAAS;4BAC7B,IAAI,QAAQ,IAAI,KAAK,QAAQ;gCAC3B,OAAO,QAAQ,IAAI;4BACrB;4BACA,IAAI,QAAQ,IAAI,KAAK,SAAS;gCAC5B,qBACE,wPAAC;oCAEC,KAAK,QAAQ,KAAK;oCAClB,KAAI;oCACJ,WAAU;mCAHL;;;;;4BAMX;wBACF;wBACC,QAAQ,MAAM,kBACb,wPAAC;4BACC,SAAS,IACP,kBAAkB;oCAChB,UAAU,QAAQ,MAAM;oCACxB,QAAQ,QAAQ,MAAM;gCACxB;4BAEF,WAAU;;8CAEV,wPAAC;oCAAI,WAAU;8CACb,cAAA,wPAAC,0MAAA,CAAA,WAAQ;wCAAC,aAAa;wCAAG,WAAU;;;;;;;;;;;8CAEtC,wPAAC;oCAAI,WAAU;;sDACb,wPAAC;4CAAK,WAAU;sDACb,QAAQ,MAAM,CAAC,KAAK;;;;;;sDAEvB,wPAAC;4CAAK,WAAU;sDAA0C;;;;;;;;;;;;;;;;;;;mBAlC3D;;;;;YA0CR,2BACC,wPAAC;gBAAI,WAAU;;kCACb,wPAAC,0MAAA,CAAA,aAAU;wBAAC,aAAa;wBAAG,WAAU;;;;;;kCACtC,wPAAC;kCAAK;;;;;;;;;;;;;;;;;;AAKhB"}}, {"offset": {"line": 1751, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1756, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst TooltipProvider = TooltipPrimitive.Provider\r\n\r\nconst Tooltip = TooltipPrimitive.Root\r\n\r\nconst TooltipTrigger = TooltipPrimitive.Trigger\r\n\r\nconst TooltipContent = React.forwardRef<\r\n  React.ElementRef<typeof TooltipPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>\r\n>(({ className, sideOffset = 4, ...props }, ref) => (\r\n  <TooltipPrimitive.Content\r\n    ref={ref}\r\n    sideOffset={sideOffset}\r\n    className={cn(\r\n      \"z-50 overflow-hidden rounded-md bg-muted px-3 py-1.5 text-xs text-muted-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTooltipContent.displayName = TooltipPrimitive.Content.displayName\r\n\r\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;;;;AAOA,MAAM,kBAAkB,oKAAiB,QAAQ;AAEjD,MAAM,UAAU,oKAAiB,IAAI;AAErC,MAAM,iBAAiB,oKAAiB,OAAO;AAE/C,MAAM,+BAAiB,gNAAM,UAAU,CAGrC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,wPAAC,oKAAiB,OAAO;QACvB,KAAK;QACL,YAAY;QACZ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,iXACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG,oKAAiB,OAAO,CAAC,WAAW"}}, {"offset": {"line": 1787, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1792, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/components/chat-input.tsx"], "sourcesContent": ["'use client'\r\n\r\n\r\nimport { But<PERSON> } from '@/components/ui/button'\r\nimport {\r\n  <PERSON><PERSON><PERSON>,\r\n  Tooltip<PERSON>ontent,\r\n  TooltipProvider,\r\n  TooltipTrigger,\r\n} from '@/components/ui/tooltip'\r\nimport { isFileInArray } from '@/lib/utils'\r\nimport { <PERSON>U<PERSON>, <PERSON>clip, Square, X } from 'lucide-react'\r\nimport { SetStateAction, useEffect, useMemo, useState } from 'react'\r\nimport TextareaAutosize from 'react-textarea-autosize'\r\n\r\nexport function ChatInput({\r\n  retry,\r\n  isErrored,\r\n  errorMessage,\r\n  isLoading,\r\n  isRateLimited,\r\n  stop,\r\n  input,\r\n  handleInputChange,\r\n  handleSubmit,\r\n  isMultiModal,\r\n  files,\r\n  handleFileChange,\r\n  children,\r\n}: {\r\n  retry: () => void\r\n  isErrored: boolean\r\n  errorMessage: string\r\n  isLoading: boolean\r\n  isRateLimited: boolean\r\n  stop: () => void\r\n  input: string\r\n  handleInputChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void\r\n  handleSubmit: (e: React.FormEvent<HTMLFormElement>) => void\r\n  isMultiModal: boolean\r\n  files: File[]\r\n  handleFileChange: (change: SetStateAction<File[]>) => void\r\n  children: React.ReactNode\r\n}) {\r\n  function handleFileInput(e: React.ChangeEvent<HTMLInputElement>) {\r\n    handleFileChange((prev) => {\r\n      const newFiles = Array.from(e.target.files || [])\r\n      const uniqueFiles = newFiles.filter((file) => !isFileInArray(file, prev))\r\n      return [...prev, ...uniqueFiles]\r\n    })\r\n  }\r\n\r\n  function handleFileRemove(file: File) {\r\n    handleFileChange((prev) => prev.filter((f) => f !== file))\r\n  }\r\n\r\n  function handlePaste(e: React.ClipboardEvent<HTMLTextAreaElement>) {\r\n    const items = Array.from(e.clipboardData.items)\r\n\r\n    for (const item of items) {\r\n      if (item.type.indexOf('image') !== -1) {\r\n        e.preventDefault()\r\n\r\n        const file = item.getAsFile()\r\n        if (file) {\r\n          handleFileChange((prev) => {\r\n            if (!isFileInArray(file, prev)) {\r\n              return [...prev, file]\r\n            }\r\n            return prev\r\n          })\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  const [dragActive, setDragActive] = useState(false)\r\n\r\n  function handleDrag(e: React.DragEvent) {\r\n    e.preventDefault()\r\n    e.stopPropagation()\r\n    if (e.type === 'dragenter' || e.type === 'dragover') {\r\n      setDragActive(true)\r\n    } else if (e.type === 'dragleave') {\r\n      setDragActive(false)\r\n    }\r\n  }\r\n\r\n  function handleDrop(e: React.DragEvent) {\r\n    e.preventDefault()\r\n    e.stopPropagation()\r\n    setDragActive(false)\r\n\r\n    const droppedFiles = Array.from(e.dataTransfer.files).filter((file) =>\r\n      file.type.startsWith('image/'),\r\n    )\r\n\r\n    if (droppedFiles.length > 0) {\r\n      handleFileChange((prev) => {\r\n        const uniqueFiles = droppedFiles.filter(\r\n          (file) => !isFileInArray(file, prev),\r\n        )\r\n        return [...prev, ...uniqueFiles]\r\n      })\r\n    }\r\n  }\r\n\r\n  const filePreview = useMemo(() => {\r\n    if (files.length === 0) return null\r\n    return Array.from(files).map((file) => {\r\n      return (\r\n        <div className=\"relative\" key={file.name}>\r\n          <span\r\n            onClick={() => handleFileRemove(file)}\r\n            className=\"absolute top-[-8] right-[-8] bg-muted rounded-full p-1\"\r\n          >\r\n            <X className=\"h-3 w-3 cursor-pointer\" />\r\n          </span>\r\n          <img\r\n            src={URL.createObjectURL(file)}\r\n            alt={file.name}\r\n            className=\"rounded-xl w-10 h-10 object-cover\"\r\n          />\r\n        </div>\r\n      )\r\n    })\r\n  }, [files])\r\n\r\n  function onEnter(e: React.KeyboardEvent<HTMLFormElement>) {\r\n    if (e.key === 'Enter' && !e.shiftKey && !e.nativeEvent.isComposing) {\r\n      e.preventDefault()\r\n      if (e.currentTarget.checkValidity()) {\r\n        handleSubmit(e)\r\n      } else {\r\n        e.currentTarget.reportValidity()\r\n      }\r\n    }\r\n  }\r\n\r\n  useEffect(() => {\r\n    if (!isMultiModal) {\r\n      handleFileChange([])\r\n    }\r\n  }, [isMultiModal])\r\n\r\n  return (\r\n    <form\r\n      onSubmit={handleSubmit}\r\n      onKeyDown={onEnter}\r\n      className=\"mb-2 mt-auto flex flex-col bg-background\"\r\n      onDragEnter={isMultiModal ? handleDrag : undefined}\r\n      onDragLeave={isMultiModal ? handleDrag : undefined}\r\n      onDragOver={isMultiModal ? handleDrag : undefined}\r\n      onDrop={isMultiModal ? handleDrop : undefined}\r\n    >\r\n      {isErrored && (\r\n        <div\r\n          className={`flex items-center p-1.5 text-sm font-medium mx-4 mb-10 rounded-xl ${\r\n            isRateLimited\r\n              ? 'bg-orange-400/10 text-orange-400'\r\n              : 'bg-red-400/10 text-red-400'\r\n          }`}\r\n        >\r\n          <span className=\"flex-1 px-1.5\">{errorMessage}</span>\r\n          <button\r\n            className={`px-2 py-1 rounded-sm ${\r\n              isRateLimited ? 'bg-orange-400/20' : 'bg-red-400/20'\r\n            }`}\r\n            onClick={retry}\r\n          >\r\n            Try again\r\n          </button>\r\n        </div>\r\n      )}\r\n      <div className=\"relative\">\r\n  \r\n        <div\r\n          className={`shadow-md rounded-2xl relative z-10 bg-background border ${\r\n            dragActive\r\n              ? 'before:absolute before:inset-0 before:rounded-2xl before:border-2 before:border-dashed before:border-primary'\r\n              : ''\r\n          }`}\r\n        >\r\n          <div className=\"flex items-center px-3 py-2 gap-1\">{children}</div>\r\n          <TextareaAutosize\r\n            autoFocus={true}\r\n            minRows={1}\r\n            maxRows={5}\r\n            className=\"text-normal px-3 resize-none ring-0 bg-inherit w-full m-0 outline-none\"\r\n            required={true}\r\n            placeholder=\"Describe your app...\"\r\n            disabled={isErrored}\r\n            value={input}\r\n            onChange={handleInputChange}\r\n            onPaste={isMultiModal ? handlePaste : undefined}\r\n          />\r\n          <div className=\"flex p-3 gap-2 items-center\">\r\n            <input\r\n              type=\"file\"\r\n              id=\"multimodal\"\r\n              name=\"multimodal\"\r\n              accept=\"image/*\"\r\n              multiple={true}\r\n              className=\"hidden\"\r\n              onChange={handleFileInput}\r\n            />\r\n            <div className=\"flex items-center flex-1 gap-2\">\r\n              <TooltipProvider>\r\n                <Tooltip delayDuration={0}>\r\n                  <TooltipTrigger asChild>\r\n                    <Button\r\n                      disabled={!isMultiModal || isErrored}\r\n                      type=\"button\"\r\n                      variant=\"outline\"\r\n                      size=\"icon\"\r\n                      className=\"rounded-xl h-10 w-10\"\r\n                      onClick={(e) => {\r\n                        e.preventDefault()\r\n                        document.getElementById('multimodal')?.click()\r\n                      }}\r\n                    >\r\n                      <Paperclip className=\"h-5 w-5\" />\r\n                    </Button>\r\n                  </TooltipTrigger>\r\n                  <TooltipContent>Add attachments</TooltipContent>\r\n                </Tooltip>\r\n              </TooltipProvider>\r\n              {files.length > 0 && filePreview}\r\n            </div>\r\n            <div>\r\n              {!isLoading ? (\r\n                <TooltipProvider>\r\n                  <Tooltip delayDuration={0}>\r\n                    <TooltipTrigger asChild>\r\n                      <Button\r\n                        disabled={isErrored}\r\n                        variant=\"default\"\r\n                        size=\"icon\"\r\n                        type=\"submit\"\r\n                        className=\"rounded-xl h-10 w-10\"\r\n                      >\r\n                        <ArrowUp className=\"h-5 w-5\" />\r\n                      </Button>\r\n                    </TooltipTrigger>\r\n                    <TooltipContent>Send message</TooltipContent>\r\n                  </Tooltip>\r\n                </TooltipProvider>\r\n              ) : (\r\n                <TooltipProvider>\r\n                  <Tooltip delayDuration={0}>\r\n                    <TooltipTrigger asChild>\r\n                      <Button\r\n                        variant=\"secondary\"\r\n                        size=\"icon\"\r\n                        className=\"rounded-xl h-10 w-10\"\r\n                        onClick={(e) => {\r\n                          e.preventDefault()\r\n                          stop()\r\n                        }}\r\n                      >\r\n                        <Square className=\"h-5 w-5\" />\r\n                      </Button>\r\n                    </TooltipTrigger>\r\n                    <TooltipContent>Stop generation</TooltipContent>\r\n                  </Tooltip>\r\n                </TooltipProvider>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n  \r\n    </form>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;;;;;;;;AAeO,SAAS,UAAU,EACxB,KAAK,EACL,SAAS,EACT,YAAY,EACZ,SAAS,EACT,aAAa,EACb,IAAI,EACJ,KAAK,EACL,iBAAiB,EACjB,YAAY,EACZ,YAAY,EACZ,KAAK,EACL,gBAAgB,EAChB,QAAQ,EAeT;IACC,SAAS,gBAAgB,CAAsC;QAC7D,iBAAiB,CAAC;YAChB,MAAM,WAAW,MAAM,IAAI,CAAC,EAAE,MAAM,CAAC,KAAK,IAAI,EAAE;YAChD,MAAM,cAAc,SAAS,MAAM,CAAC,CAAC,OAAS,CAAC,CAAA,GAAA,4GAAA,CAAA,gBAAa,AAAD,EAAE,MAAM;YACnE,OAAO;mBAAI;mBAAS;aAAY;QAClC;IACF;IAEA,SAAS,iBAAiB,IAAU;QAClC,iBAAiB,CAAC,OAAS,KAAK,MAAM,CAAC,CAAC,IAAM,MAAM;IACtD;IAEA,SAAS,YAAY,CAA4C;QAC/D,MAAM,QAAQ,MAAM,IAAI,CAAC,EAAE,aAAa,CAAC,KAAK;QAE9C,KAAK,MAAM,QAAQ,MAAO;YACxB,IAAI,KAAK,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,GAAG;gBACrC,EAAE,cAAc;gBAEhB,MAAM,OAAO,KAAK,SAAS;gBAC3B,IAAI,MAAM;oBACR,iBAAiB,CAAC;wBAChB,IAAI,CAAC,CAAA,GAAA,4GAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,OAAO;4BAC9B,OAAO;mCAAI;gCAAM;6BAAK;wBACxB;wBACA,OAAO;oBACT;gBACF;YACF;QACF;IACF;IAEA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,SAAS,WAAW,CAAkB;QACpC,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,IAAI,EAAE,IAAI,KAAK,eAAe,EAAE,IAAI,KAAK,YAAY;YACnD,cAAc;QAChB,OAAO,IAAI,EAAE,IAAI,KAAK,aAAa;YACjC,cAAc;QAChB;IACF;IAEA,SAAS,WAAW,CAAkB;QACpC,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,cAAc;QAEd,MAAM,eAAe,MAAM,IAAI,CAAC,EAAE,YAAY,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,OAC5D,KAAK,IAAI,CAAC,UAAU,CAAC;QAGvB,IAAI,aAAa,MAAM,GAAG,GAAG;YAC3B,iBAAiB,CAAC;gBAChB,MAAM,cAAc,aAAa,MAAM,CACrC,CAAC,OAAS,CAAC,CAAA,GAAA,4GAAA,CAAA,gBAAa,AAAD,EAAE,MAAM;gBAEjC,OAAO;uBAAI;uBAAS;iBAAY;YAClC;QACF;IACF;IAEA,MAAM,cAAc,CAAA,GAAA,+MAAA,CAAA,UAAO,AAAD,EAAE;QAC1B,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO;QAC/B,OAAO,MAAM,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;YAC5B,qBACE,wPAAC;gBAAI,WAAU;;kCACb,wPAAC;wBACC,SAAS,IAAM,iBAAiB;wBAChC,WAAU;kCAEV,cAAA,wPAAC,4LAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;;;;;;kCAEf,wPAAC;wBACC,KAAK,IAAI,eAAe,CAAC;wBACzB,KAAK,KAAK,IAAI;wBACd,WAAU;;;;;;;eAViB,KAAK,IAAI;;;;;QAc5C;IACF,GAAG;QAAC;KAAM;IAEV,SAAS,QAAQ,CAAuC;QACtD,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,IAAI,CAAC,EAAE,WAAW,CAAC,WAAW,EAAE;YAClE,EAAE,cAAc;YAChB,IAAI,EAAE,aAAa,CAAC,aAAa,IAAI;gBACnC,aAAa;YACf,OAAO;gBACL,EAAE,aAAa,CAAC,cAAc;YAChC;QACF;IACF;IAEA,CAAA,GAAA,+MAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,cAAc;YACjB,iBAAiB,EAAE;QACrB;IACF,GAAG;QAAC;KAAa;IAEjB,qBACE,wPAAC;QACC,UAAU;QACV,WAAW;QACX,WAAU;QACV,aAAa,eAAe,aAAa;QACzC,aAAa,eAAe,aAAa;QACzC,YAAY,eAAe,aAAa;QACxC,QAAQ,eAAe,aAAa;;YAEnC,2BACC,wPAAC;gBACC,WAAW,CAAC,kEAAkE,EAC5E,gBACI,qCACA,6BACL,CAAC;;kCAEF,wPAAC;wBAAK,WAAU;kCAAiB;;;;;;kCACjC,wPAAC;wBACC,WAAW,CAAC,qBAAqB,EAC/B,gBAAgB,qBAAqB,gBACtC,CAAC;wBACF,SAAS;kCACV;;;;;;;;;;;;0BAKL,wPAAC;gBAAI,WAAU;0BAEb,cAAA,wPAAC;oBACC,WAAW,CAAC,yDAAyD,EACnE,aACI,iHACA,GACL,CAAC;;sCAEF,wPAAC;4BAAI,WAAU;sCAAqC;;;;;;sCACpD,wPAAC,4MAAA,CAAA,UAAgB;4BACf,WAAW;4BACX,SAAS;4BACT,SAAS;4BACT,WAAU;4BACV,UAAU;4BACV,aAAY;4BACZ,UAAU;4BACV,OAAO;4BACP,UAAU;4BACV,SAAS,eAAe,cAAc;;;;;;sCAExC,wPAAC;4BAAI,WAAU;;8CACb,wPAAC;oCACC,MAAK;oCACL,IAAG;oCACH,MAAK;oCACL,QAAO;oCACP,UAAU;oCACV,WAAU;oCACV,UAAU;;;;;;8CAEZ,wPAAC;oCAAI,WAAU;;sDACb,wPAAC,4HAAA,CAAA,kBAAe;sDACd,cAAA,wPAAC,4HAAA,CAAA,UAAO;gDAAC,eAAe;;kEACtB,wPAAC,4HAAA,CAAA,iBAAc;wDAAC,OAAO;kEACrB,cAAA,wPAAC,2HAAA,CAAA,SAAM;4DACL,UAAU,CAAC,gBAAgB;4DAC3B,MAAK;4DACL,SAAQ;4DACR,MAAK;4DACL,WAAU;4DACV,SAAS,CAAC;gEACR,EAAE,cAAc;gEAChB,SAAS,cAAc,CAAC,eAAe;4DACzC;sEAEA,cAAA,wPAAC,4MAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;;;;;;;;;;;kEAGzB,wPAAC,4HAAA,CAAA,iBAAc;kEAAC;;;;;;;;;;;;;;;;;wCAGnB,MAAM,MAAM,GAAG,KAAK;;;;;;;8CAEvB,wPAAC;8CACE,CAAC,0BACA,wPAAC,4HAAA,CAAA,kBAAe;kDACd,cAAA,wPAAC,4HAAA,CAAA,UAAO;4CAAC,eAAe;;8DACtB,wPAAC,4HAAA,CAAA,iBAAc;oDAAC,OAAO;8DACrB,cAAA,wPAAC,2HAAA,CAAA,SAAM;wDACL,UAAU;wDACV,SAAQ;wDACR,MAAK;wDACL,MAAK;wDACL,WAAU;kEAEV,cAAA,wPAAC,4MAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;;;;;;;;;;;8DAGvB,wPAAC,4HAAA,CAAA,iBAAc;8DAAC;;;;;;;;;;;;;;;;6DAIpB,wPAAC,4HAAA,CAAA,kBAAe;kDACd,cAAA,wPAAC,4HAAA,CAAA,UAAO;4CAAC,eAAe;;8DACtB,wPAAC,4HAAA,CAAA,iBAAc;oDAAC,OAAO;8DACrB,cAAA,wPAAC,2HAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,WAAU;wDACV,SAAS,CAAC;4DACR,EAAE,cAAc;4DAChB;wDACF;kEAEA,cAAA,wPAAC,sMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;;;;;;;;;;;8DAGtB,wPAAC,4HAAA,CAAA,iBAAc;8DAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWpC"}}, {"offset": {"line": 2196, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2201, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport {\r\n  CaretSortIcon,\r\n  CheckIcon,\r\n  ChevronDownIcon,\r\n  ChevronUpIcon,\r\n} from \"@radix-ui/react-icons\"\r\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Select = SelectPrimitive.Root\r\n\r\nconst SelectGroup = SelectPrimitive.Group\r\n\r\nconst SelectValue = SelectPrimitive.Value\r\n\r\nconst SelectTrigger = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <SelectPrimitive.Icon asChild>\r\n      <ChevronDownIcon className=\"pl-1 h-4 w-4 opacity-50\" />\r\n    </SelectPrimitive.Icon>\r\n  </SelectPrimitive.Trigger>\r\n))\r\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\r\n\r\nconst SelectScrollUpButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollUpButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronUpIcon />\r\n  </SelectPrimitive.ScrollUpButton>\r\n))\r\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\r\n\r\nconst SelectScrollDownButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollDownButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronDownIcon />\r\n  </SelectPrimitive.ScrollDownButton>\r\n))\r\nSelectScrollDownButton.displayName =\r\n  SelectPrimitive.ScrollDownButton.displayName\r\n\r\nconst SelectContent = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\r\n>(({ className, children, position = \"popper\", ...props }, ref) => (\r\n  <SelectPrimitive.Portal>\r\n    <SelectPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n        position === \"popper\" &&\r\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\r\n        className\r\n      )}\r\n      position={position}\r\n      {...props}\r\n    >\r\n      <SelectScrollUpButton />\r\n      <SelectPrimitive.Viewport\r\n        className={cn(\r\n          \"p-1\",\r\n          position === \"popper\" &&\r\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\r\n        )}\r\n      >\r\n        {children}\r\n      </SelectPrimitive.Viewport>\r\n      <SelectScrollDownButton />\r\n    </SelectPrimitive.Content>\r\n  </SelectPrimitive.Portal>\r\n))\r\nSelectContent.displayName = SelectPrimitive.Content.displayName\r\n\r\nconst SelectLabel = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Label\r\n    ref={ref}\r\n    className={cn(\"px-2 py-1.5 text-sm font-semibold text-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSelectLabel.displayName = SelectPrimitive.Label.displayName\r\n\r\nconst SelectItem = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute right-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <SelectPrimitive.ItemIndicator>\r\n        <CheckIcon className=\"h-4 w-4\" />\r\n      </SelectPrimitive.ItemIndicator>\r\n    </span>\r\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n  </SelectPrimitive.Item>\r\n))\r\nSelectItem.displayName = SelectPrimitive.Item.displayName\r\n\r\nconst SelectSeparator = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Separator\r\n    ref={ref}\r\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\r\n\r\nexport {\r\n  Select,\r\n  SelectGroup,\r\n  SelectValue,\r\n  SelectTrigger,\r\n  SelectContent,\r\n  SelectLabel,\r\n  SelectItem,\r\n  SelectSeparator,\r\n  SelectScrollUpButton,\r\n  SelectScrollDownButton,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;;;;;;AAaA,MAAM,SAAS,mKAAgB,IAAI;AAEnC,MAAM,cAAc,mKAAgB,KAAK;AAEzC,MAAM,cAAc,mKAAgB,KAAK;AAEzC,MAAM,8BAAgB,gNAAM,UAAU,CAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,wPAAC,mKAAgB,OAAO;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,2TACA;QAED,GAAG,KAAK;;YAER;0BACD,wPAAC,mKAAgB,IAAI;gBAAC,OAAO;0BAC3B,cAAA,wPAAC,gLAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAIjC,cAAc,WAAW,GAAG,mKAAgB,OAAO,CAAC,WAAW;AAE/D,MAAM,qCAAuB,gNAAM,UAAU,CAG3C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,wPAAC,mKAAgB,cAAc;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,wPAAC,gLAAA,CAAA,gBAAa;;;;;;;;;;AAGlB,qBAAqB,WAAW,GAAG,mKAAgB,cAAc,CAAC,WAAW;AAE7E,MAAM,uCAAyB,gNAAM,UAAU,CAG7C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,wPAAC,mKAAgB,gBAAgB;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,wPAAC,gLAAA,CAAA,kBAAe;;;;;;;;;;AAGpB,uBAAuB,WAAW,GAChC,mKAAgB,gBAAgB,CAAC,WAAW;AAE9C,MAAM,8BAAgB,gNAAM,UAAU,CAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,wPAAC,mKAAgB,MAAM;kBACrB,cAAA,wPAAC,mKAAgB,OAAO;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,wPAAC;;;;;8BACD,wPAAC,mKAAgB,QAAQ;oBACvB,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,wPAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,mKAAgB,OAAO,CAAC,WAAW;AAE/D,MAAM,4BAAc,gNAAM,UAAU,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,wPAAC,mKAAgB,KAAK;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,qDAAqD;QAClE,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAgB,KAAK,CAAC,WAAW;AAE3D,MAAM,2BAAa,gNAAM,UAAU,CAGjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,wPAAC,mKAAgB,IAAI;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,wPAAC;gBAAK,WAAU;0BACd,cAAA,wPAAC,mKAAgB,aAAa;8BAC5B,cAAA,wPAAC,gLAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,wPAAC,mKAAgB,QAAQ;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,mKAAgB,IAAI,CAAC,WAAW;AAEzD,MAAM,gCAAkB,gNAAM,UAAU,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,wPAAC,mKAAgB,SAAS;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,mKAAgB,SAAS,CAAC,WAAW"}}, {"offset": {"line": 2382, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2387, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/components/chat-picker.tsx"], "sourcesContent": ["import {\r\n  Select,\r\n  SelectContent,\r\n  SelectGroup,\r\n  SelectItem,\r\n  SelectLabel,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from '@/components/ui/select'\r\nimport { LLMModel, LLMModelConfig } from '@/lib/models'\r\nimport { TemplateId, Templates } from '@/lib/templates'\r\nimport 'core-js/features/object/group-by.js'\r\nimport { Sparkles } from 'lucide-react'\r\nimport Image from 'next/image'\r\n\r\nexport function ChatPicker({\r\n  templates,\r\n  selectedTemplate,\r\n  onSelectedTemplateChange,\r\n  models,\r\n  languageModel,\r\n  onLanguageModelChange,\r\n}: {\r\n  templates: Templates\r\n  selectedTemplate: 'auto' | TemplateId\r\n  onSelectedTemplateChange: (template: 'auto' | TemplateId) => void\r\n  models: LLMModel[]\r\n  languageModel: LLMModelConfig\r\n  onLanguageModelChange: (config: LLMModelConfig) => void\r\n}) {\r\n  return (\r\n    <div className=\"flex items-center space-x-2\">\r\n      <div className=\"flex flex-col\">\r\n        <Select\r\n          name=\"template\"\r\n          defaultValue={selectedTemplate}\r\n          onValueChange={onSelectedTemplateChange}\r\n        >\r\n          <SelectTrigger className=\"whitespace-nowrap border-none shadow-none focus:ring-0 px-0 py-0 h-6 text-xs\">\r\n            <SelectValue placeholder=\"Select a persona\" />\r\n          </SelectTrigger>\r\n          <SelectContent side=\"top\">\r\n            <SelectGroup>\r\n              <SelectLabel>Persona</SelectLabel>\r\n              <SelectItem value=\"auto\">\r\n                <div className=\"flex items-center space-x-2\">\r\n                  <Sparkles\r\n                    className=\"flex text-[#a1a1aa]\"\r\n                    width={14}\r\n                    height={14}\r\n                  />\r\n                  <span>Auto</span>\r\n                </div>\r\n              </SelectItem>\r\n              {Object.entries(templates).map(([templateId, template]) => (\r\n                <SelectItem key={templateId} value={templateId}>\r\n                  <div className=\"flex items-center space-x-2\">\r\n                    <Image\r\n                      className=\"flex\"\r\n                      src={`/thirdparty/templates/${templateId}.svg`}\r\n                      alt={templateId}\r\n                      width={14}\r\n                      height={14}\r\n                    />\r\n                    <span>{template.name}</span>\r\n                  </div>\r\n                </SelectItem>\r\n              ))}\r\n            </SelectGroup>\r\n          </SelectContent>\r\n        </Select>\r\n      </div>\r\n      <div className=\"flex flex-col\">\r\n        <Select\r\n          name=\"languageModel\"\r\n          defaultValue={languageModel.model}\r\n          onValueChange={(e) => onLanguageModelChange({ model: e })}\r\n        >\r\n          <SelectTrigger className=\"whitespace-nowrap border-none shadow-none focus:ring-0 px-0 py-0 h-6 text-xs\">\r\n            <SelectValue placeholder=\"Language model\" />\r\n          </SelectTrigger>\r\n          <SelectContent>\r\n            {Object.entries(\r\n              Object.groupBy(models, ({ provider }) => provider),\r\n            ).map(([provider, models]) => (\r\n              <SelectGroup key={provider}>\r\n                <SelectLabel>{provider}</SelectLabel>\r\n                {models?.map((model) => (\r\n                  <SelectItem key={model.id} value={model.id}>\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <Image\r\n                        className=\"flex\"\r\n                        src={`/thirdparty/logos/${model.providerId}.svg`}\r\n                        alt={model.provider}\r\n                        width={14}\r\n                        height={14}\r\n                      />\r\n                      <span>{model.name}</span>\r\n                    </div>\r\n                  </SelectItem>\r\n                ))}\r\n              </SelectGroup>\r\n            ))}\r\n          </SelectContent>\r\n        </Select>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAeO,SAAS,WAAW,EACzB,SAAS,EACT,gBAAgB,EAChB,wBAAwB,EACxB,MAAM,EACN,aAAa,EACb,qBAAqB,EAQtB;IACC,qBACE,wPAAC;QAAI,WAAU;;0BACb,wPAAC;gBAAI,WAAU;0BACb,cAAA,wPAAC,2HAAA,CAAA,SAAM;oBACL,MAAK;oBACL,cAAc;oBACd,eAAe;;sCAEf,wPAAC,2HAAA,CAAA,gBAAa;4BAAC,WAAU;sCACvB,cAAA,wPAAC,2HAAA,CAAA,cAAW;gCAAC,aAAY;;;;;;;;;;;sCAE3B,wPAAC,2HAAA,CAAA,gBAAa;4BAAC,MAAK;sCAClB,cAAA,wPAAC,2HAAA,CAAA,cAAW;;kDACV,wPAAC,2HAAA,CAAA,cAAW;kDAAC;;;;;;kDACb,wPAAC,2HAAA,CAAA,aAAU;wCAAC,OAAM;kDAChB,cAAA,wPAAC;4CAAI,WAAU;;8DACb,wPAAC,0MAAA,CAAA,WAAQ;oDACP,WAAU;oDACV,OAAO;oDACP,QAAQ;;;;;;8DAEV,wPAAC;8DAAK;;;;;;;;;;;;;;;;;oCAGT,OAAO,OAAO,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC,YAAY,SAAS,iBACpD,wPAAC,2HAAA,CAAA,aAAU;4CAAkB,OAAO;sDAClC,cAAA,wPAAC;gDAAI,WAAU;;kEACb,wPAAC,6HAAA,CAAA,UAAK;wDACJ,WAAU;wDACV,KAAK,CAAC,sBAAsB,EAAE,WAAW,IAAI,CAAC;wDAC9C,KAAK;wDACL,OAAO;wDACP,QAAQ;;;;;;kEAEV,wPAAC;kEAAM,SAAS,IAAI;;;;;;;;;;;;2CATP;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAiB3B,wPAAC;gBAAI,WAAU;0BACb,cAAA,wPAAC,2HAAA,CAAA,SAAM;oBACL,MAAK;oBACL,cAAc,cAAc,KAAK;oBACjC,eAAe,CAAC,IAAM,sBAAsB;4BAAE,OAAO;wBAAE;;sCAEvD,wPAAC,2HAAA,CAAA,gBAAa;4BAAC,WAAU;sCACvB,cAAA,wPAAC,2HAAA,CAAA,cAAW;gCAAC,aAAY;;;;;;;;;;;sCAE3B,wPAAC,2HAAA,CAAA,gBAAa;sCACX,OAAO,OAAO,CACb,OAAO,OAAO,CAAC,QAAQ,CAAC,EAAE,QAAQ,EAAE,GAAK,WACzC,GAAG,CAAC,CAAC,CAAC,UAAU,OAAO,iBACvB,wPAAC,2HAAA,CAAA,cAAW;;sDACV,wPAAC,2HAAA,CAAA,cAAW;sDAAE;;;;;;wCACb,QAAQ,IAAI,CAAC,sBACZ,wPAAC,2HAAA,CAAA,aAAU;gDAAgB,OAAO,MAAM,EAAE;0DACxC,cAAA,wPAAC;oDAAI,WAAU;;sEACb,wPAAC,6HAAA,CAAA,UAAK;4DACJ,WAAU;4DACV,KAAK,CAAC,kBAAkB,EAAE,MAAM,UAAU,CAAC,IAAI,CAAC;4DAChD,KAAK,MAAM,QAAQ;4DACnB,OAAO;4DACP,QAAQ;;;;;;sEAEV,wPAAC;sEAAM,MAAM,IAAI;;;;;;;;;;;;+CATJ,MAAM,EAAE;;;;;;mCAHX;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBhC"}}, {"offset": {"line": 2621, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2626, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\r\nimport {\r\n  CheckIcon,\r\n  ChevronRightIcon,\r\n  DotFilledIcon,\r\n} from \"@radix-ui/react-icons\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst DropdownMenu = DropdownMenuPrimitive.Root\r\n\r\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger\r\n\r\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group\r\n\r\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal\r\n\r\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub\r\n\r\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup\r\n\r\nconst DropdownMenuSubTrigger = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\r\n    inset?: boolean\r\n  }\r\n>(({ className, inset, children, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.SubTrigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent\",\r\n      inset && \"pl-8\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <ChevronRightIcon className=\"ml-auto h-4 w-4\" />\r\n  </DropdownMenuPrimitive.SubTrigger>\r\n))\r\nDropdownMenuSubTrigger.displayName =\r\n  DropdownMenuPrimitive.SubTrigger.displayName\r\n\r\nconst DropdownMenuSubContent = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\r\n>(({ className, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.SubContent\r\n    ref={ref}\r\n    className={cn(\r\n      \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDropdownMenuSubContent.displayName =\r\n  DropdownMenuPrimitive.SubContent.displayName\r\n\r\nconst DropdownMenuContent = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\r\n>(({ className, sideOffset = 4, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Portal>\r\n    <DropdownMenuPrimitive.Content\r\n      ref={ref}\r\n      sideOffset={sideOffset}\r\n      className={cn(\r\n        \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md\",\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  </DropdownMenuPrimitive.Portal>\r\n))\r\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName\r\n\r\nconst DropdownMenuItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\r\n    inset?: boolean\r\n  }\r\n>(({ className, inset, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      inset && \"pl-8\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName\r\n\r\nconst DropdownMenuCheckboxItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\r\n>(({ className, children, checked, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.CheckboxItem\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    checked={checked}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <DropdownMenuPrimitive.ItemIndicator>\r\n        <CheckIcon className=\"h-4 w-4\" />\r\n      </DropdownMenuPrimitive.ItemIndicator>\r\n    </span>\r\n    {children}\r\n  </DropdownMenuPrimitive.CheckboxItem>\r\n))\r\nDropdownMenuCheckboxItem.displayName =\r\n  DropdownMenuPrimitive.CheckboxItem.displayName\r\n\r\nconst DropdownMenuRadioItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\r\n>(({ className, children, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.RadioItem\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <DropdownMenuPrimitive.ItemIndicator>\r\n        <DotFilledIcon className=\"h-4 w-4 fill-current\" />\r\n      </DropdownMenuPrimitive.ItemIndicator>\r\n    </span>\r\n    {children}\r\n  </DropdownMenuPrimitive.RadioItem>\r\n))\r\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName\r\n\r\nconst DropdownMenuLabel = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\r\n    inset?: boolean\r\n  }\r\n>(({ className, inset, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Label\r\n    ref={ref}\r\n    className={cn(\r\n      \"px-2 py-1.5 text-sm font-semibold\",\r\n      inset && \"pl-8\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName\r\n\r\nconst DropdownMenuSeparator = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Separator\r\n    ref={ref}\r\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\r\n    {...props}\r\n  />\r\n))\r\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName\r\n\r\nconst DropdownMenuShortcut = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLSpanElement>) => {\r\n  return (\r\n    <span\r\n      className={cn(\"ml-auto text-xs tracking-widest opacity-60\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\"\r\n\r\nexport {\r\n  DropdownMenu,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuRadioItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuShortcut,\r\n  DropdownMenuGroup,\r\n  DropdownMenuPortal,\r\n  DropdownMenuSub,\r\n  DropdownMenuSubContent,\r\n  DropdownMenuSubTrigger,\r\n  DropdownMenuRadioGroup,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;;AAYA,MAAM,eAAe,6KAAsB,IAAI;AAE/C,MAAM,sBAAsB,6KAAsB,OAAO;AAEzD,MAAM,oBAAoB,6KAAsB,KAAK;AAErD,MAAM,qBAAqB,6KAAsB,MAAM;AAEvD,MAAM,kBAAkB,6KAAsB,GAAG;AAEjD,MAAM,yBAAyB,6KAAsB,UAAU;AAE/D,MAAM,uCAAyB,gNAAM,UAAU,CAK7C,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAC3C,wPAAC,6KAAsB,UAAU;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wIACA,SAAS,QACT;QAED,GAAG,KAAK;;YAER;0BACD,wPAAC,gLAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGhC,uBAAuB,WAAW,GAChC,6KAAsB,UAAU,CAAC,WAAW;AAE9C,MAAM,uCAAyB,gNAAM,UAAU,CAG7C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,wPAAC,6KAAsB,UAAU;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,ybACA;QAED,GAAG,KAAK;;;;;;AAGb,uBAAuB,WAAW,GAChC,6KAAsB,UAAU,CAAC,WAAW;AAE9C,MAAM,oCAAsB,gNAAM,UAAU,CAG1C,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,wPAAC,6KAAsB,MAAM;kBAC3B,cAAA,wPAAC,6KAAsB,OAAO;YAC5B,KAAK;YACL,YAAY;YACZ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wGACA,oVACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIf,oBAAoB,WAAW,GAAG,6KAAsB,OAAO,CAAC,WAAW;AAE3E,MAAM,iCAAmB,gNAAM,UAAU,CAKvC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,wPAAC,6KAAsB,IAAI;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,mOACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG,6KAAsB,IAAI,CAAC,WAAW;AAErE,MAAM,yCAA2B,gNAAM,UAAU,CAG/C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBAC7C,wPAAC,6KAAsB,YAAY;QACjC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,wPAAC;gBAAK,WAAU;0BACd,cAAA,wPAAC,6KAAsB,aAAa;8BAClC,cAAA,wPAAC,gLAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGL,yBAAyB,WAAW,GAClC,6KAAsB,YAAY,CAAC,WAAW;AAEhD,MAAM,sCAAwB,gNAAM,UAAU,CAG5C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,wPAAC,6KAAsB,SAAS;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAED,GAAG,KAAK;;0BAET,wPAAC;gBAAK,WAAU;0BACd,cAAA,wPAAC,6KAAsB,aAAa;8BAClC,cAAA,wPAAC,gLAAA,CAAA,gBAAa;wBAAC,WAAU;;;;;;;;;;;;;;;;YAG5B;;;;;;;AAGL,sBAAsB,WAAW,GAAG,6KAAsB,SAAS,CAAC,WAAW;AAE/E,MAAM,kCAAoB,gNAAM,UAAU,CAKxC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,wPAAC,6KAAsB,KAAK;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,qCACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,6KAAsB,KAAK,CAAC,WAAW;AAEvE,MAAM,sCAAwB,gNAAM,UAAU,CAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,wPAAC,6KAAsB,SAAS;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,sBAAsB,WAAW,GAAG,6KAAsB,SAAS,CAAC,WAAW;AAE/E,MAAM,uBAAuB,CAAC,EAC5B,SAAS,EACT,GAAG,OACmC;IACtC,qBACE,wPAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,8CAA8C;QAC3D,GAAG,KAAK;;;;;;AAGf;AACA,qBAAqB,WAAW,GAAG"}}, {"offset": {"line": 2817, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2822, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/components/chat-settings.tsx"], "sourcesContent": ["import { Button } from './ui/button'\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from './ui/dropdown-menu'\r\nimport { Input } from './ui/input'\r\nimport { Label } from './ui/label'\r\nimport {\r\n  Tooltip,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger,\r\n} from './ui/tooltip'\r\nimport { LLMModelConfig } from '@/lib/models'\r\nimport { Settings2 } from 'lucide-react'\r\n\r\nexport function ChatSettings({\r\n  apiKeyConfigurable,\r\n  baseURLConfigurable,\r\n  languageModel,\r\n  onLanguageModelChange,\r\n}: {\r\n  apiKeyConfigurable: boolean\r\n  baseURLConfigurable: boolean\r\n  languageModel: LLMModelConfig\r\n  onLanguageModelChange: (model: LLMModelConfig) => void\r\n}) {\r\n  return (\r\n    <DropdownMenu>\r\n      <TooltipProvider>\r\n        <Tooltip delayDuration={0}>\r\n          <TooltipTrigger asChild>\r\n            <DropdownMenuTrigger asChild>\r\n              <Button variant=\"ghost\" size=\"icon\" className=\"text-muted-foreground h-6 w-6 rounded-sm\">\r\n                <Settings2 className=\"h-4 w-4\" />\r\n              </Button>\r\n            </DropdownMenuTrigger>\r\n          </TooltipTrigger>\r\n          <TooltipContent>LLM settings</TooltipContent>\r\n        </Tooltip>\r\n      </TooltipProvider>\r\n      <DropdownMenuContent align=\"start\">\r\n        {apiKeyConfigurable && (\r\n          <>\r\n            <div className=\"flex flex-col gap-2 px-2 py-2\">\r\n              <Label htmlFor=\"apiKey\">API Key</Label>\r\n              <Input\r\n                name=\"apiKey\"\r\n                type=\"password\"\r\n                placeholder=\"Auto\"\r\n                required={true}\r\n                defaultValue={languageModel.apiKey}\r\n                onChange={(e) =>\r\n                  onLanguageModelChange({\r\n                    apiKey:\r\n                      e.target.value.length > 0 ? e.target.value : undefined,\r\n                  })\r\n                }\r\n                className=\"text-sm\"\r\n              />\r\n            </div>\r\n            <DropdownMenuSeparator />\r\n          </>\r\n        )}\r\n        {baseURLConfigurable && (\r\n          <>\r\n            <div className=\"flex flex-col gap-2 px-2 py-2\">\r\n              <Label htmlFor=\"baseURL\">Base URL</Label>\r\n              <Input\r\n                name=\"baseURL\"\r\n                type=\"text\"\r\n                placeholder=\"Auto\"\r\n                required={true}\r\n                defaultValue={languageModel.baseURL}\r\n                onChange={(e) =>\r\n                  onLanguageModelChange({\r\n                    baseURL:\r\n                      e.target.value.length > 0 ? e.target.value : undefined,\r\n                  })\r\n                }\r\n                className=\"text-sm\"\r\n              />\r\n            </div>\r\n            <DropdownMenuSeparator />\r\n          </>\r\n        )}\r\n        <div className=\"flex flex-col gap-1.5 px-2 py-2\">\r\n          <span className=\"text-sm font-medium\">Parameters</span>\r\n          <div className=\"flex space-x-4 items-center\">\r\n            <span className=\"text-sm flex-1 text-muted-foreground\">\r\n              Output tokens\r\n            </span>\r\n            <Input\r\n              type=\"number\"\r\n              defaultValue={languageModel.maxTokens}\r\n              min={50}\r\n              max={10000}\r\n              step={1}\r\n              className=\"h-6 rounded-sm w-[84px] text-xs text-center tabular-nums\"\r\n              placeholder=\"Auto\"\r\n              onChange={(e) =>\r\n                onLanguageModelChange({\r\n                  maxTokens: parseFloat(e.target.value) || undefined,\r\n                })\r\n              }\r\n            />\r\n          </div>\r\n          <div className=\"flex space-x-4 items-center\">\r\n            <span className=\"text-sm flex-1 text-muted-foreground\">\r\n              Temperature\r\n            </span>\r\n            <Input\r\n              type=\"number\"\r\n              defaultValue={languageModel.temperature}\r\n              min={0}\r\n              max={5}\r\n              step={0.01}\r\n              className=\"h-6 rounded-sm w-[84px] text-xs text-center tabular-nums\"\r\n              placeholder=\"Auto\"\r\n              onChange={(e) =>\r\n                onLanguageModelChange({\r\n                  temperature: parseFloat(e.target.value) || undefined,\r\n                })\r\n              }\r\n            />\r\n          </div>\r\n          <div className=\"flex space-x-4 items-center\">\r\n            <span className=\"text-sm flex-1 text-muted-foreground\">Top P</span>\r\n            <Input\r\n              type=\"number\"\r\n              defaultValue={languageModel.topP}\r\n              min={0}\r\n              max={1}\r\n              step={0.01}\r\n              className=\"h-6 rounded-sm w-[84px] text-xs text-center tabular-nums\"\r\n              placeholder=\"Auto\"\r\n              onChange={(e) =>\r\n                onLanguageModelChange({\r\n                  topP: parseFloat(e.target.value) || undefined,\r\n                })\r\n              }\r\n            />\r\n          </div>\r\n          <div className=\"flex space-x-4 items-center\">\r\n            <span className=\"text-sm flex-1 text-muted-foreground\">Top K</span>\r\n            <Input\r\n              type=\"number\"\r\n              defaultValue={languageModel.topK}\r\n              min={0}\r\n              max={500}\r\n              step={1}\r\n              className=\"h-6 rounded-sm w-[84px] text-xs text-center tabular-nums\"\r\n              placeholder=\"Auto\"\r\n              onChange={(e) =>\r\n                onLanguageModelChange({\r\n                  topK: parseFloat(e.target.value) || undefined,\r\n                })\r\n              }\r\n            />\r\n          </div>\r\n          <div className=\"flex space-x-4 items-center\">\r\n            <span className=\"text-sm flex-1 text-muted-foreground\">\r\n              Frequence penalty\r\n            </span>\r\n            <Input\r\n              type=\"number\"\r\n              defaultValue={languageModel.frequencyPenalty}\r\n              min={0}\r\n              max={2}\r\n              step={0.01}\r\n              className=\"h-6 rounded-sm w-[84px] text-xs text-center tabular-nums\"\r\n              placeholder=\"Auto\"\r\n              onChange={(e) =>\r\n                onLanguageModelChange({\r\n                  frequencyPenalty: parseFloat(e.target.value) || undefined,\r\n                })\r\n              }\r\n            />\r\n          </div>\r\n          <div className=\"flex space-x-4 items-center\">\r\n            <span className=\"text-sm flex-1 text-muted-foreground\">\r\n              Presence penalty\r\n            </span>\r\n            <Input\r\n              type=\"number\"\r\n              defaultValue={languageModel.presencePenalty}\r\n              min={0}\r\n              max={2}\r\n              step={0.01}\r\n              className=\"h-6 rounded-sm w-[84px] text-xs text-center tabular-nums\"\r\n              placeholder=\"Auto\"\r\n              onChange={(e) =>\r\n                onLanguageModelChange({\r\n                  presencePenalty: parseFloat(e.target.value) || undefined,\r\n                })\r\n              }\r\n            />\r\n          </div>\r\n        </div>\r\n      </DropdownMenuContent>\r\n    </DropdownMenu>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAkBO,SAAS,aAAa,EAC3B,kBAAkB,EAClB,mBAAmB,EACnB,aAAa,EACb,qBAAqB,EAMtB;IACC,qBACE,wPAAC,qIAAA,CAAA,eAAY;;0BACX,wPAAC,4HAAA,CAAA,kBAAe;0BACd,cAAA,wPAAC,4HAAA,CAAA,UAAO;oBAAC,eAAe;;sCACtB,wPAAC,4HAAA,CAAA,iBAAc;4BAAC,OAAO;sCACrB,cAAA,wPAAC,qIAAA,CAAA,sBAAmB;gCAAC,OAAO;0CAC1B,cAAA,wPAAC,2HAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAO,WAAU;8CAC5C,cAAA,wPAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;sCAI3B,wPAAC,4HAAA,CAAA,iBAAc;sCAAC;;;;;;;;;;;;;;;;;0BAGpB,wPAAC,qIAAA,CAAA,sBAAmB;gBAAC,OAAM;;oBACxB,oCACC;;0CACE,wPAAC;gCAAI,WAAU;;kDACb,wPAAC,0HAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAS;;;;;;kDACxB,wPAAC,0HAAA,CAAA,QAAK;wCACJ,MAAK;wCACL,MAAK;wCACL,aAAY;wCACZ,UAAU;wCACV,cAAc,cAAc,MAAM;wCAClC,UAAU,CAAC,IACT,sBAAsB;gDACpB,QACE,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,EAAE,MAAM,CAAC,KAAK,GAAG;4CACjD;wCAEF,WAAU;;;;;;;;;;;;0CAGd,wPAAC,qIAAA,CAAA,wBAAqB;;;;;;;oBAGzB,qCACC;;0CACE,wPAAC;gCAAI,WAAU;;kDACb,wPAAC,0HAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAU;;;;;;kDACzB,wPAAC,0HAAA,CAAA,QAAK;wCACJ,MAAK;wCACL,MAAK;wCACL,aAAY;wCACZ,UAAU;wCACV,cAAc,cAAc,OAAO;wCACnC,UAAU,CAAC,IACT,sBAAsB;gDACpB,SACE,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,EAAE,MAAM,CAAC,KAAK,GAAG;4CACjD;wCAEF,WAAU;;;;;;;;;;;;0CAGd,wPAAC,qIAAA,CAAA,wBAAqB;;;;;;;kCAG1B,wPAAC;wBAAI,WAAU;;0CACb,wPAAC;gCAAK,WAAU;0CAAsB;;;;;;0CACtC,wPAAC;gCAAI,WAAU;;kDACb,wPAAC;wCAAK,WAAU;kDAAuC;;;;;;kDAGvD,wPAAC,0HAAA,CAAA,QAAK;wCACJ,MAAK;wCACL,cAAc,cAAc,SAAS;wCACrC,KAAK;wCACL,KAAK;wCACL,MAAM;wCACN,WAAU;wCACV,aAAY;wCACZ,UAAU,CAAC,IACT,sBAAsB;gDACpB,WAAW,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;4CAC3C;;;;;;;;;;;;0CAIN,wPAAC;gCAAI,WAAU;;kDACb,wPAAC;wCAAK,WAAU;kDAAuC;;;;;;kDAGvD,wPAAC,0HAAA,CAAA,QAAK;wCACJ,MAAK;wCACL,cAAc,cAAc,WAAW;wCACvC,KAAK;wCACL,KAAK;wCACL,MAAM;wCACN,WAAU;wCACV,aAAY;wCACZ,UAAU,CAAC,IACT,sBAAsB;gDACpB,aAAa,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;4CAC7C;;;;;;;;;;;;0CAIN,wPAAC;gCAAI,WAAU;;kDACb,wPAAC;wCAAK,WAAU;kDAAuC;;;;;;kDACvD,wPAAC,0HAAA,CAAA,QAAK;wCACJ,MAAK;wCACL,cAAc,cAAc,IAAI;wCAChC,KAAK;wCACL,KAAK;wCACL,MAAM;wCACN,WAAU;wCACV,aAAY;wCACZ,UAAU,CAAC,IACT,sBAAsB;gDACpB,MAAM,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;4CACtC;;;;;;;;;;;;0CAIN,wPAAC;gCAAI,WAAU;;kDACb,wPAAC;wCAAK,WAAU;kDAAuC;;;;;;kDACvD,wPAAC,0HAAA,CAAA,QAAK;wCACJ,MAAK;wCACL,cAAc,cAAc,IAAI;wCAChC,KAAK;wCACL,KAAK;wCACL,MAAM;wCACN,WAAU;wCACV,aAAY;wCACZ,UAAU,CAAC,IACT,sBAAsB;gDACpB,MAAM,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;4CACtC;;;;;;;;;;;;0CAIN,wPAAC;gCAAI,WAAU;;kDACb,wPAAC;wCAAK,WAAU;kDAAuC;;;;;;kDAGvD,wPAAC,0HAAA,CAAA,QAAK;wCACJ,MAAK;wCACL,cAAc,cAAc,gBAAgB;wCAC5C,KAAK;wCACL,KAAK;wCACL,MAAM;wCACN,WAAU;wCACV,aAAY;wCACZ,UAAU,CAAC,IACT,sBAAsB;gDACpB,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;4CAClD;;;;;;;;;;;;0CAIN,wPAAC;gCAAI,WAAU;;kDACb,wPAAC;wCAAK,WAAU;kDAAuC;;;;;;kDAGvD,wPAAC,0HAAA,CAAA,QAAK;wCACJ,MAAK;wCACL,cAAc,cAAc,eAAe;wCAC3C,KAAK;wCACL,KAAK;wCACL,MAAM;wCACN,WAAU;wCACV,aAAY;wCACZ,UAAU,CAAC,IACT,sBAAsB;gDACpB,iBAAiB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;4CACjD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhB"}}, {"offset": {"line": 3208, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3213, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/components/client-only.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\n\ninterface ClientOnlyProps {\n  children: React.ReactNode\n  fallback?: React.ReactNode\n}\n\nexport function ClientOnly({ children, fallback = null }: ClientOnlyProps) {\n  const [mounted, setMounted] = useState(false)\n\n  useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  if (!mounted) {\n    return <>{fallback}</>\n  }\n\n  return <>{children}</>\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;;AASO,SAAS,WAAW,EAAE,QAAQ,EAAE,WAAW,IAAI,EAAmB;IACvE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,+MAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,qBAAO;sBAAG;;IACZ;IAEA,qBAAO;kBAAG;;AACZ"}}, {"offset": {"line": 3236, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3241, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Avatar = React.forwardRef<\r\n  React.ElementRef<typeof AvatarPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nAvatar.displayName = AvatarPrimitive.Root.displayName\r\n\r\nconst AvatarImage = React.forwardRef<\r\n  React.ElementRef<typeof AvatarPrimitive.Image>,\r\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image>\r\n>(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Image\r\n    ref={ref}\r\n    className={cn(\"aspect-square h-full w-full\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAvatarImage.displayName = AvatarPrimitive.Image.displayName\r\n\r\nconst AvatarFallback = React.forwardRef<\r\n  React.ElementRef<typeof AvatarPrimitive.Fallback>,\r\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback>\r\n>(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Fallback\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex h-full w-full items-center justify-center rounded-full bg-muted\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nAvatarFallback.displayName = AvatarPrimitive.Fallback.displayName\r\n\r\nexport { Avatar, AvatarImage, AvatarFallback }\r\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;;;;AAOA,MAAM,uBAAS,gNAAM,UAAU,CAG7B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,wPAAC,mKAAgB,IAAI;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,OAAO,WAAW,GAAG,mKAAgB,IAAI,CAAC,WAAW;AAErD,MAAM,4BAAc,gNAAM,UAAU,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,wPAAC,mKAAgB,KAAK;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAgB,KAAK,CAAC,WAAW;AAE3D,MAAM,+BAAiB,gNAAM,UAAU,CAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,wPAAC,mKAAgB,QAAQ;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wEACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG,mKAAgB,QAAQ,CAAC,WAAW"}}, {"offset": {"line": 3287, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3292, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/components/ui/theme-toggle.tsx"], "sourcesContent": ["import { Button } from '@/components/ui/button'\r\nimport { MoonIcon, SunIcon } from 'lucide-react'\r\nimport { useTheme } from 'next-themes'\r\nimport { useState, useEffect, forwardRef } from 'react'\r\n\r\nexport const ThemeToggle = forwardRef<\r\n  HTMLButtonElement,\r\n  {\r\n    className?: string\r\n  }\r\n>(({ className, ...props }, ref) => {\r\n  const { setTheme, theme } = useTheme()\r\n  const [mounted, setMounted] = useState(false)\r\n\r\n  // useEffect only runs on the client, so now we can safely show the UI\r\n  useEffect(() => {\r\n    setMounted(true)\r\n  }, [])\r\n\r\n  if (!mounted) {\r\n    return (\r\n      <Button\r\n        {...props}\r\n        ref={ref}\r\n        variant=\"ghost\"\r\n        size=\"icon\"\r\n        className={className}\r\n        disabled\r\n      >\r\n        <div className=\"h-4 w-4 md:h-5 md:w-5 animate-pulse bg-current opacity-20 rounded\" />\r\n      </Button>\r\n    )\r\n  }\r\n\r\n  return (\r\n    <Button\r\n      {...props}\r\n      ref={ref}\r\n      variant=\"ghost\"\r\n      size=\"icon\"\r\n      className={className}\r\n      onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}\r\n    >\r\n      {theme === 'light' ? (\r\n        <SunIcon className=\"h-4 w-4 md:h-5 md:w-5\" />\r\n      ) : (\r\n        <MoonIcon className=\"h-4 w-4 md:h-5 md:w-5\" />\r\n      )}\r\n    </Button>\r\n  )\r\n})\r\n\r\nThemeToggle.displayName = 'ThemeToggle'\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAKO,MAAM,4BAAc,CAAA,GAAA,+MAAA,CAAA,aAAU,AAAD,EAKlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,sEAAsE;IACtE,CAAA,GAAA,+MAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,qBACE,wPAAC,2HAAA,CAAA,SAAM;YACJ,GAAG,KAAK;YACT,KAAK;YACL,SAAQ;YACR,MAAK;YACL,WAAW;YACX,QAAQ;sBAER,cAAA,wPAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,wPAAC,2HAAA,CAAA,SAAM;QACJ,GAAG,KAAK;QACT,KAAK;QACL,SAAQ;QACR,MAAK;QACL,WAAW;QACX,SAAS,IAAM,SAAS,UAAU,SAAS,UAAU;kBAEpD,UAAU,wBACT,wPAAC,oMAAA,CAAA,UAAO;YAAC,WAAU;;;;;iCAEnB,wPAAC,sMAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;;;;;;AAI5B;AAEA,YAAY,WAAW,GAAG"}}, {"offset": {"line": 3362, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3367, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/components/navbar.tsx"], "sourcesContent": ["import Logo from './logo'\r\nimport { Avatar, AvatarImage } from '@/components/ui/avatar'\r\nimport { Button } from '@/components/ui/button'\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from '@/components/ui/dropdown-menu'\r\nimport { ThemeToggle } from '@/components/ui/theme-toggle'\r\nimport {\r\n  Tooltip,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger,\r\n} from '@/components/ui/tooltip'\r\n\r\nimport { Session } from '@supabase/supabase-js'\r\nimport { ArrowRight, LogOut, Trash, Undo } from 'lucide-react'\r\nimport Link from 'next/link'\r\n\r\nexport function NavBar({\r\n  session,\r\n  showLogin,\r\n  signOut,\r\n  onClear,\r\n  canClear,\r\n  onSocialClick,\r\n  onUndo,\r\n  canUndo,\r\n}: {\r\n  session: Session | null\r\n  showLogin: () => void\r\n  signOut: () => void\r\n  onClear: () => void\r\n  canClear: boolean\r\n  onSocialClick: (target: 'github' | 'x' | 'discord') => void\r\n  onUndo: () => void\r\n  canUndo: boolean\r\n}) {\r\n  return (\r\n    <nav className=\"w-full flex bg-background py-4\">\r\n      <div className=\"flex flex-1 items-center\">\r\n        <Link href=\"/\" className=\"flex items-center gap-2\" target=\"_blank\">\r\n          <Logo width={24} height={24} />\r\n          <h1 className=\"whitespace-pre\">Photoscience</h1>\r\n        </Link>\r\n      </div>\r\n      <div className=\"flex items-center gap-1 md:gap-4\">\r\n        <TooltipProvider>\r\n          <Tooltip delayDuration={0}>\r\n            <TooltipTrigger asChild>\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"icon\"\r\n                onClick={onUndo}\r\n                disabled={!canUndo}\r\n              >\r\n                <Undo className=\"h-4 w-4 md:h-5 md:w-5\" />\r\n              </Button>\r\n            </TooltipTrigger>\r\n            <TooltipContent>Undo</TooltipContent>\r\n          </Tooltip>\r\n        </TooltipProvider>\r\n        <TooltipProvider>\r\n          <Tooltip delayDuration={0}>\r\n            <TooltipTrigger asChild>\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"icon\"\r\n                onClick={onClear}\r\n                disabled={!canClear}\r\n              >\r\n                <Trash className=\"h-4 w-4 md:h-5 md:w-5\" />\r\n              </Button>\r\n            </TooltipTrigger>\r\n            <TooltipContent>Clear chat</TooltipContent>\r\n          </Tooltip>\r\n        </TooltipProvider>\r\n        <TooltipProvider>\r\n          <Tooltip delayDuration={0}>\r\n            <TooltipTrigger asChild>\r\n              <ThemeToggle />\r\n            </TooltipTrigger>\r\n            <TooltipContent>Toggle theme</TooltipContent>\r\n          </Tooltip>\r\n        </TooltipProvider>\r\n        {session ? (\r\n          <DropdownMenu>\r\n            <TooltipProvider>\r\n              <Tooltip delayDuration={0}>\r\n                <TooltipTrigger asChild>\r\n                  <DropdownMenuTrigger asChild>\r\n                    <Avatar className=\"w-8 h-8\">\r\n                      <AvatarImage\r\n                        src={\r\n                          session.user.user_metadata?.avatar_url ||\r\n                          'https://avatar.vercel.sh/' + session.user.email\r\n                        }\r\n                        alt={session.user.email}\r\n                      />\r\n                    </Avatar>\r\n                  </DropdownMenuTrigger>\r\n                </TooltipTrigger>\r\n                <TooltipContent>My Account</TooltipContent>\r\n              </Tooltip>\r\n            </TooltipProvider>\r\n            <DropdownMenuContent className=\"w-56\" align=\"end\">\r\n              <DropdownMenuLabel className=\"flex flex-col\">\r\n                <span className=\"text-sm\">My Account</span>\r\n                <span className=\"text-xs text-muted-foreground\">\r\n                  {session.user.email}\r\n                </span>\r\n              </DropdownMenuLabel>\r\n              <DropdownMenuSeparator />\r\n            \r\n            \r\n              <DropdownMenuItem onClick={signOut}>\r\n                <LogOut className=\"mr-2 h-4 w-4 text-muted-foreground\" />\r\n                Sign out\r\n              </DropdownMenuItem>\r\n            </DropdownMenuContent>\r\n          </DropdownMenu>\r\n        ) : (\r\n          <Button variant=\"default\" onClick={showLogin}>\r\n            Sign in\r\n            <ArrowRight className=\"ml-2 h-4 w-4\" />\r\n          </Button>\r\n        )}\r\n      </div>\r\n    </nav>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAuBO,SAAS,OAAO,EACrB,OAAO,EACP,SAAS,EACT,OAAO,EACP,OAAO,EACP,QAAQ,EACR,aAAa,EACb,MAAM,EACN,OAAO,EAUR;IACC,qBACE,wPAAC;QAAI,WAAU;;0BACb,wPAAC;gBAAI,WAAU;0BACb,cAAA,wPAAC,4HAAA,CAAA,UAAI;oBAAC,MAAK;oBAAI,WAAU;oBAA0B,QAAO;;sCACxD,wPAAC,mHAAA,CAAA,UAAI;4BAAC,OAAO;4BAAI,QAAQ;;;;;;sCACzB,wPAAC;4BAAG,WAAU;sCAAiB;;;;;;;;;;;;;;;;;0BAGnC,wPAAC;gBAAI,WAAU;;kCACb,wPAAC,4HAAA,CAAA,kBAAe;kCACd,cAAA,wPAAC,4HAAA,CAAA,UAAO;4BAAC,eAAe;;8CACtB,wPAAC,4HAAA,CAAA,iBAAc;oCAAC,OAAO;8CACrB,cAAA,wPAAC,2HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,UAAU,CAAC;kDAEX,cAAA,wPAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;8CAGpB,wPAAC,4HAAA,CAAA,iBAAc;8CAAC;;;;;;;;;;;;;;;;;kCAGpB,wPAAC,4HAAA,CAAA,kBAAe;kCACd,cAAA,wPAAC,4HAAA,CAAA,UAAO;4BAAC,eAAe;;8CACtB,wPAAC,4HAAA,CAAA,iBAAc;oCAAC,OAAO;8CACrB,cAAA,wPAAC,2HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,UAAU,CAAC;kDAEX,cAAA,wPAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;;;;;;8CAGrB,wPAAC,4HAAA,CAAA,iBAAc;8CAAC;;;;;;;;;;;;;;;;;kCAGpB,wPAAC,4HAAA,CAAA,kBAAe;kCACd,cAAA,wPAAC,4HAAA,CAAA,UAAO;4BAAC,eAAe;;8CACtB,wPAAC,4HAAA,CAAA,iBAAc;oCAAC,OAAO;8CACrB,cAAA,wPAAC,oIAAA,CAAA,cAAW;;;;;;;;;;8CAEd,wPAAC,4HAAA,CAAA,iBAAc;8CAAC;;;;;;;;;;;;;;;;;oBAGnB,wBACC,wPAAC,qIAAA,CAAA,eAAY;;0CACX,wPAAC,4HAAA,CAAA,kBAAe;0CACd,cAAA,wPAAC,4HAAA,CAAA,UAAO;oCAAC,eAAe;;sDACtB,wPAAC,4HAAA,CAAA,iBAAc;4CAAC,OAAO;sDACrB,cAAA,wPAAC,qIAAA,CAAA,sBAAmB;gDAAC,OAAO;0DAC1B,cAAA,wPAAC,2HAAA,CAAA,SAAM;oDAAC,WAAU;8DAChB,cAAA,wPAAC,2HAAA,CAAA,cAAW;wDACV,KACE,QAAQ,IAAI,CAAC,aAAa,EAAE,cAC5B,8BAA8B,QAAQ,IAAI,CAAC,KAAK;wDAElD,KAAK,QAAQ,IAAI,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;sDAK/B,wPAAC,4HAAA,CAAA,iBAAc;sDAAC;;;;;;;;;;;;;;;;;0CAGpB,wPAAC,qIAAA,CAAA,sBAAmB;gCAAC,WAAU;gCAAO,OAAM;;kDAC1C,wPAAC,qIAAA,CAAA,oBAAiB;wCAAC,WAAU;;0DAC3B,wPAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,wPAAC;gDAAK,WAAU;0DACb,QAAQ,IAAI,CAAC,KAAK;;;;;;;;;;;;kDAGvB,wPAAC,qIAAA,CAAA,wBAAqB;;;;;kDAGtB,wPAAC,qIAAA,CAAA,mBAAgB;wCAAC,SAAS;;0DACzB,wPAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAuC;;;;;;;;;;;;;;;;;;6CAM/D,wPAAC,2HAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,SAAS;;4BAAW;0CAE5C,wPAAC,kNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMlC"}}, {"offset": {"line": 3705, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3710, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/components/ui/copy-button.tsx"], "sourcesContent": ["import { Button, ButtonProps } from './button'\r\nimport { Check, Copy } from 'lucide-react'\r\nimport { useState, forwardRef } from 'react'\r\n\r\nexport const CopyButton = forwardRef<\r\n  HTMLButtonElement,\r\n  {\r\n    variant?: ButtonProps['variant']\r\n    content: string\r\n    onCopy?: () => void\r\n    className?: string\r\n  }\r\n>(({ variant = 'ghost', content, onCopy, className, ...props }, ref) => {\r\n  const [copied, setCopied] = useState(false)\r\n\r\n  function copy(content: string) {\r\n    setCopied(true)\r\n    navigator.clipboard.writeText(content)\r\n    setTimeout(() => setCopied(false), 1000)\r\n    onCopy?.()\r\n  }\r\n\r\n  return (\r\n    <Button\r\n      {...props}\r\n      ref={ref}\r\n      variant={variant}\r\n      size=\"icon\"\r\n      className={className}\r\n      onClick={() => copy(content)}\r\n    >\r\n      {copied ? <Check className=\"h-4 w-4\" /> : <Copy className=\"h-4 w-4\" />}\r\n    </Button>\r\n  )\r\n})\r\n\r\nCopyButton.displayName = 'CopyButton'\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAIO,MAAM,2BAAa,CAAA,GAAA,+MAAA,CAAA,aAAU,AAAD,EAQjC,CAAC,EAAE,UAAU,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC9D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,SAAS,KAAK,OAAe;QAC3B,UAAU;QACV,UAAU,SAAS,CAAC,SAAS,CAAC;QAC9B,WAAW,IAAM,UAAU,QAAQ;QACnC;IACF;IAEA,qBACE,wPAAC,2HAAA,CAAA,SAAM;QACJ,GAAG,KAAK;QACT,KAAK;QACL,SAAS;QACT,MAAK;QACL,WAAW;QACX,SAAS,IAAM,KAAK;kBAEnB,uBAAS,wPAAC,oMAAA,CAAA,QAAK;YAAC,WAAU;;;;;iCAAe,wPAAC,kMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;;;;;;AAGhE;AAEA,WAAW,WAAW,GAAG"}}, {"offset": {"line": 3758, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3763, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 3770, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3775, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/components/deploy-dialog.tsx"], "sourcesContent": ["import Logo from './logo'\r\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './ui/copy-button'\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectGroup,\r\n  SelectItem,\r\n  SelectLabel,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from './ui/select'\r\nimport { publish } from '@/app/actions/publish'\r\nimport { Button } from '@/components/ui/button'\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuTrigger,\r\n} from '@/components/ui/dropdown-menu'\r\nimport { Input } from '@/components/ui/input'\r\nimport { Duration } from '@/lib/duration'\r\nimport { usePostHog } from 'posthog-js/react'\r\nimport { useEffect, useState } from 'react'\r\n\r\nexport function DeployDialog({\r\n  url,\r\n  sbxId,\r\n  teamID,\r\n  accessToken,\r\n}: {\r\n  url: string\r\n  sbxId: string\r\n  teamID: string | undefined\r\n  accessToken: string | undefined\r\n}) {\r\n  const posthog = usePostHog()\r\n\r\n  const [publishedURL, setPublishedURL] = useState<string | null>(null)\r\n  const [duration, setDuration] = useState<string | null>(null)\r\n\r\n  useEffect(() => {\r\n    setPublishedURL(null)\r\n  }, [url])\r\n\r\n  async function publishURL(e: React.FormEvent<HTMLFormElement>) {\r\n    e.preventDefault()\r\n    const { url: publishedURL } = await publish(\r\n      url,\r\n      sbxId,\r\n      duration as Duration,\r\n      teamID,\r\n      accessToken,\r\n    )\r\n    setPublishedURL(publishedURL)\r\n    posthog.capture('publish_url', {\r\n      url: publishedURL,\r\n    })\r\n  }\r\n\r\n  return (\r\n    <DropdownMenu>\r\n      <DropdownMenuTrigger asChild>\r\n        <Button variant=\"default\">\r\n          <Logo style=\"e2b\" width={16} height={16} className=\"mr-2\" />\r\n          Deploy to E2B\r\n        </Button>\r\n      </DropdownMenuTrigger>\r\n      <DropdownMenuContent className=\"p-4 w-80 flex flex-col gap-2\">\r\n        <div className=\"text-sm font-semibold\">Deploy to E2B</div>\r\n        <div className=\"text-sm text-muted-foreground\">\r\n          Deploying the fragment will make it publicly accessible to others via\r\n          link.\r\n        </div>\r\n        <div className=\"text-sm text-muted-foreground\">\r\n          The fragment will be available up until the expiration date you choose\r\n          and you&apos;ll be billed based on our{' '}\r\n          <a\r\n            href=\"https://e2b.dev/docs/pricing\"\r\n            target=\"_blank\"\r\n            className=\"underline\"\r\n          >\r\n            Compute pricing\r\n          </a>\r\n          .\r\n        </div>\r\n        <div className=\"text-sm text-muted-foreground\">\r\n          All new accounts receive $100 worth of compute credits. Upgrade to{' '}\r\n          <a\r\n            href=\"https://e2b.dev/dashboard?tab=billing\"\r\n            target=\"_blank\"\r\n            className=\"underline\"\r\n          >\r\n            Pro tier\r\n          </a>{' '}\r\n          for longer expiration.\r\n        </div>\r\n        <form className=\"flex flex-col gap-2\" onSubmit={publishURL}>\r\n          {publishedURL ? (\r\n            <div className=\"flex items-center gap-2\">\r\n              <Input value={publishedURL} readOnly />\r\n              <CopyButton content={publishedURL} />\r\n            </div>\r\n          ) : (\r\n            <Select onValueChange={(value) => setDuration(value)} required>\r\n              <SelectTrigger>\r\n                <SelectValue placeholder=\"Set expiration\" />\r\n              </SelectTrigger>\r\n              <SelectContent>\r\n                <SelectGroup>\r\n                  <SelectLabel>Expires in</SelectLabel>\r\n                  <SelectItem value=\"30m\">30 Minutes</SelectItem>\r\n                  <SelectItem value=\"1h\">1 Hour</SelectItem>\r\n                  <SelectItem value=\"3h\">3 Hours · Pro</SelectItem>\r\n                  <SelectItem value=\"6h\">6 Hours · Pro</SelectItem>\r\n                  <SelectItem value=\"1d\">1 Day · Pro</SelectItem>\r\n                </SelectGroup>\r\n              </SelectContent>\r\n            </Select>\r\n          )}\r\n          <Button\r\n            type=\"submit\"\r\n            variant=\"default\"\r\n            disabled={publishedURL !== null}\r\n          >\r\n            {publishedURL ? 'Deployed' : 'Accept and deploy'}\r\n          </Button>\r\n        </form>\r\n      </DropdownMenuContent>\r\n    </DropdownMenu>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAuBO,SAAS,aAAa,EAC3B,GAAG,EACH,KAAK,EACL,MAAM,EACN,WAAW,EAMZ;IACC,MAAM,UAAU,CAAA,GAAA,8JAAA,CAAA,aAAU,AAAD;IAEzB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAiB;IAExD,CAAA,GAAA,+MAAA,CAAA,YAAS,AAAD,EAAE;QACR,gBAAgB;IAClB,GAAG;QAAC;KAAI;IAER,eAAe,WAAW,CAAmC;QAC3D,EAAE,cAAc;QAChB,MAAM,EAAE,KAAK,YAAY,EAAE,GAAG,MAAM,CAAA,GAAA,yHAAA,CAAA,UAAO,AAAD,EACxC,KACA,OACA,UACA,QACA;QAEF,gBAAgB;QAChB,QAAQ,OAAO,CAAC,eAAe;YAC7B,KAAK;QACP;IACF;IAEA,qBACE,wPAAC,qIAAA,CAAA,eAAY;;0BACX,wPAAC,qIAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,wPAAC,2HAAA,CAAA,SAAM;oBAAC,SAAQ;;sCACd,wPAAC,mHAAA,CAAA,UAAI;4BAAC,OAAM;4BAAM,OAAO;4BAAI,QAAQ;4BAAI,WAAU;;;;;;wBAAS;;;;;;;;;;;;0BAIhE,wPAAC,qIAAA,CAAA,sBAAmB;gBAAC,WAAU;;kCAC7B,wPAAC;wBAAI,WAAU;kCAAwB;;;;;;kCACvC,wPAAC;wBAAI,WAAU;kCAAgC;;;;;;kCAI/C,wPAAC;wBAAI,WAAU;;4BAAgC;4BAEN;0CACvC,wPAAC;gCACC,MAAK;gCACL,QAAO;gCACP,WAAU;0CACX;;;;;;4BAEG;;;;;;;kCAGN,wPAAC;wBAAI,WAAU;;4BAAgC;4BACsB;0CACnE,wPAAC;gCACC,MAAK;gCACL,QAAO;gCACP,WAAU;0CACX;;;;;;4BAEI;4BAAI;;;;;;;kCAGX,wPAAC;wBAAK,WAAU;wBAAsB,UAAU;;4BAC7C,6BACC,wPAAC;gCAAI,WAAU;;kDACb,wPAAC,0HAAA,CAAA,QAAK;wCAAC,OAAO;wCAAc,QAAQ;;;;;;kDACpC,wPAAC,mIAAA,CAAA,aAAU;wCAAC,SAAS;;;;;;;;;;;qDAGvB,wPAAC,2HAAA,CAAA,SAAM;gCAAC,eAAe,CAAC,QAAU,YAAY;gCAAQ,QAAQ;;kDAC5D,wPAAC,2HAAA,CAAA,gBAAa;kDACZ,cAAA,wPAAC,2HAAA,CAAA,cAAW;4CAAC,aAAY;;;;;;;;;;;kDAE3B,wPAAC,2HAAA,CAAA,gBAAa;kDACZ,cAAA,wPAAC,2HAAA,CAAA,cAAW;;8DACV,wPAAC,2HAAA,CAAA,cAAW;8DAAC;;;;;;8DACb,wPAAC,2HAAA,CAAA,aAAU;oDAAC,OAAM;8DAAM;;;;;;8DACxB,wPAAC,2HAAA,CAAA,aAAU;oDAAC,OAAM;8DAAK;;;;;;8DACvB,wPAAC,2HAAA,CAAA,aAAU;oDAAC,OAAM;8DAAK;;;;;;8DACvB,wPAAC,2HAAA,CAAA,aAAU;oDAAC,OAAM;8DAAK;;;;;;8DACvB,wPAAC,2HAAA,CAAA,aAAU;oDAAC,OAAM;8DAAK;;;;;;;;;;;;;;;;;;;;;;;0CAK/B,wPAAC,2HAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAQ;gCACR,UAAU,iBAAiB;0CAE1B,eAAe,aAAa;;;;;;;;;;;;;;;;;;;;;;;;AAMzC"}}, {"offset": {"line": 4049, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4054, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/components/code-view.tsx"], "sourcesContent": ["// import \"prismjs/plugins/line-numbers/prism-line-numbers.js\";\r\n// import \"prismjs/plugins/line-numbers/prism-line-numbers.css\";\r\nimport './code-theme.css'\r\nimport Prism from 'prismjs'\r\nimport 'prismjs/components/prism-javascript'\r\nimport 'prismjs/components/prism-jsx'\r\nimport 'prismjs/components/prism-python'\r\nimport 'prismjs/components/prism-tsx'\r\nimport 'prismjs/components/prism-typescript'\r\nimport { useEffect } from 'react'\r\n\r\nexport function CodeView({ code, lang }: { code: string; lang: string }) {\r\n  useEffect(() => {\r\n    Prism.highlightAll()\r\n  }, [code])\r\n\r\n  return (\r\n    <pre\r\n      className=\"p-4 pt-2\"\r\n      style={{\r\n        fontSize: 12,\r\n        backgroundColor: 'transparent',\r\n        borderRadius: 0,\r\n        margin: 0,\r\n      }}\r\n    >\r\n      <code className={`language-${lang}`}>{code}</code>\r\n    </pre>\r\n  )\r\n}\r\n"], "names": [], "mappings": "AAAA,+DAA+D;AAC/D,gEAAgE;;;;;;;;;;;;;;;;;;;;;;AAUzD,SAAS,SAAS,EAAE,IAAI,EAAE,IAAI,EAAkC;IACrE,CAAA,GAAA,+MAAA,CAAA,YAAS,AAAD,EAAE;QACR,gIAAA,CAAA,UAAK,CAAC,YAAY;IACpB,GAAG;QAAC;KAAK;IAET,qBACE,wPAAC;QACC,WAAU;QACV,OAAO;YACL,UAAU;YACV,iBAAiB;YACjB,cAAc;YACd,QAAQ;QACV;kBAEA,cAAA,wPAAC;YAAK,WAAW,CAAC,SAAS,EAAE,KAAK,CAAC;sBAAG;;;;;;;;;;;AAG5C"}}, {"offset": {"line": 4105, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4110, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/components/fragment-code.tsx"], "sourcesContent": ["import { CodeView } from './code-view'\r\nimport { But<PERSON> } from './ui/button'\r\nimport { CopyButton } from './ui/copy-button'\r\nimport {\r\n  <PERSON><PERSON><PERSON>,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger,\r\n} from '@/components/ui/tooltip'\r\nimport { Download, FileText } from 'lucide-react'\r\nimport { useState } from 'react'\r\n\r\nexport function FragmentCode({\r\n  files,\r\n}: {\r\n  files: { name: string; content: string }[]\r\n}) {\r\n  const [currentFile, setCurrentFile] = useState(files[0].name)\r\n  const currentFileContent = files.find(\r\n    (file) => file.name === currentFile,\r\n  )?.content\r\n\r\n  function download(filename: string, content: string) {\r\n    const blob = new Blob([content], { type: 'text/plain' })\r\n    const url = window.URL.createObjectURL(blob)\r\n    const a = document.createElement('a')\r\n    a.style.display = 'none'\r\n    a.href = url\r\n    a.download = filename\r\n    document.body.appendChild(a)\r\n    a.click()\r\n    window.URL.revokeObjectURL(url)\r\n    document.body.removeChild(a)\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex flex-col h-full\">\r\n      <div className=\"flex items-center px-2 pt-1 gap-2\">\r\n        <div className=\"flex flex-1 gap-2 overflow-x-auto\">\r\n          {files.map((file) => (\r\n            <div\r\n              key={file.name}\r\n              className={`flex gap-2 select-none cursor-pointer items-center text-sm text-muted-foreground px-2 py-1 rounded-md hover:bg-muted border ${\r\n                file.name === currentFile ? 'bg-muted border-muted' : ''\r\n              }`}\r\n              onClick={() => setCurrentFile(file.name)}\r\n            >\r\n              <FileText className=\"h-4 w-4\" />\r\n              {file.name}\r\n            </div>\r\n          ))}\r\n        </div>\r\n        <div className=\"flex items-center gap-2\">\r\n          <TooltipProvider>\r\n            <Tooltip delayDuration={0}>\r\n              <TooltipTrigger asChild>\r\n                <CopyButton\r\n                  content={currentFileContent || ''}\r\n                  className=\"text-muted-foreground\"\r\n                />\r\n              </TooltipTrigger>\r\n              <TooltipContent side=\"bottom\">Copy</TooltipContent>\r\n            </Tooltip>\r\n          </TooltipProvider>\r\n          <TooltipProvider>\r\n            <Tooltip delayDuration={0}>\r\n              <TooltipTrigger asChild>\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  size=\"icon\"\r\n                  className=\"text-muted-foreground\"\r\n                  onClick={() =>\r\n                    download(currentFile, currentFileContent || '')\r\n                  }\r\n                >\r\n                  <Download className=\"h-4 w-4\" />\r\n                </Button>\r\n              </TooltipTrigger>\r\n              <TooltipContent side=\"bottom\">Download</TooltipContent>\r\n            </Tooltip>\r\n          </TooltipProvider>\r\n        </div>\r\n      </div>\r\n      <div className=\"flex flex-col flex-1 overflow-x-auto\">\r\n        <CodeView\r\n          code={currentFileContent || ''}\r\n          lang={currentFile.split('.').pop() || ''}\r\n        />\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAYO,SAAS,aAAa,EAC3B,KAAK,EAGN;IACC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,CAAC,EAAE,CAAC,IAAI;IAC5D,MAAM,qBAAqB,MAAM,IAAI,CACnC,CAAC,OAAS,KAAK,IAAI,KAAK,cACvB;IAEH,SAAS,SAAS,QAAgB,EAAE,OAAe;QACjD,MAAM,OAAO,IAAI,KAAK;YAAC;SAAQ,EAAE;YAAE,MAAM;QAAa;QACtD,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;QACvC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,KAAK,CAAC,OAAO,GAAG;QAClB,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG;QACb,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,EAAE,KAAK;QACP,OAAO,GAAG,CAAC,eAAe,CAAC;QAC3B,SAAS,IAAI,CAAC,WAAW,CAAC;IAC5B;IAEA,qBACE,wPAAC;QAAI,WAAU;;0BACb,wPAAC;gBAAI,WAAU;;kCACb,wPAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,qBACV,wPAAC;gCAEC,WAAW,CAAC,4HAA4H,EACtI,KAAK,IAAI,KAAK,cAAc,0BAA0B,GACvD,CAAC;gCACF,SAAS,IAAM,eAAe,KAAK,IAAI;;kDAEvC,wPAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCACnB,KAAK,IAAI;;+BAPL,KAAK,IAAI;;;;;;;;;;kCAWpB,wPAAC;wBAAI,WAAU;;0CACb,wPAAC,4HAAA,CAAA,kBAAe;0CACd,cAAA,wPAAC,4HAAA,CAAA,UAAO;oCAAC,eAAe;;sDACtB,wPAAC,4HAAA,CAAA,iBAAc;4CAAC,OAAO;sDACrB,cAAA,wPAAC,mIAAA,CAAA,aAAU;gDACT,SAAS,sBAAsB;gDAC/B,WAAU;;;;;;;;;;;sDAGd,wPAAC,4HAAA,CAAA,iBAAc;4CAAC,MAAK;sDAAS;;;;;;;;;;;;;;;;;0CAGlC,wPAAC,4HAAA,CAAA,kBAAe;0CACd,cAAA,wPAAC,4HAAA,CAAA,UAAO;oCAAC,eAAe;;sDACtB,wPAAC,4HAAA,CAAA,iBAAc;4CAAC,OAAO;sDACrB,cAAA,wPAAC,2HAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS,IACP,SAAS,aAAa,sBAAsB;0DAG9C,cAAA,wPAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;;;;;sDAGxB,wPAAC,4HAAA,CAAA,iBAAc;4CAAC,MAAK;sDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAKtC,wPAAC;gBAAI,WAAU;0BACb,cAAA,wPAAC,2HAAA,CAAA,WAAQ;oBACP,MAAM,sBAAsB;oBAC5B,MAAM,YAAY,KAAK,CAAC,KAAK,GAAG,MAAM;;;;;;;;;;;;;;;;;AAKhD"}}, {"offset": {"line": 4301, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4306, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/components/fragment-interpreter.tsx"], "sourcesContent": ["import { <PERSON><PERSON>, AlertTitle, AlertDescription } from '@/components/ui/alert'\r\nimport { ExecutionResultInterpreter } from '@/lib/types'\r\nimport { Terminal } from 'lucide-react'\r\nimport Image from 'next/image'\r\n\r\nfunction LogsOutput({\r\n  stdout,\r\n  stderr,\r\n}: {\r\n  stdout: string[]\r\n  stderr: string[]\r\n}) {\r\n  if (stdout.length === 0 && stderr.length === 0) return null\r\n\r\n  return (\r\n    <div className=\"w-full h-32 max-h-32 overflow-y-auto flex flex-col items-start justify-start space-y-1 p-4\">\r\n      {stdout &&\r\n        stdout.length > 0 &&\r\n        stdout.map((out: string, index: number) => (\r\n          <pre key={index} className=\"text-xs\">\r\n            {out}\r\n          </pre>\r\n        ))}\r\n      {stderr &&\r\n        stderr.length > 0 &&\r\n        stderr.map((err: string, index: number) => (\r\n          <pre key={index} className=\"text-xs text-red-500\">\r\n            {err}\r\n          </pre>\r\n        ))}\r\n    </div>\r\n  )\r\n}\r\n\r\nexport function FragmentInterpreter({\r\n  result,\r\n}: {\r\n  result: ExecutionResultInterpreter\r\n}) {\r\n  const { cellResults, stdout, stderr, runtimeError } = result\r\n\r\n  // The AI-generated code experienced runtime error\r\n  if (runtimeError) {\r\n    const { name, value, traceback } = runtimeError\r\n    return (\r\n      <div className=\"p-4\">\r\n        <Alert variant=\"destructive\">\r\n          <Terminal className=\"h-4 w-4\" />\r\n          <AlertTitle>\r\n            {name}: {value}\r\n          </AlertTitle>\r\n          <AlertDescription className=\"font-mono whitespace-pre-wrap\">\r\n            {traceback}\r\n          </AlertDescription>\r\n        </Alert>\r\n      </div>\r\n    )\r\n  }\r\n\r\n  // Cell results can contain text, pdfs, images, and code (html, latex, json)\r\n  // TODO: Show all results\r\n  // TODO: Check other formats than `png`\r\n  if (cellResults.length > 0) {\r\n    const imgInBase64 = cellResults[0].png\r\n    return (\r\n      <div className=\"flex flex-col h-full\">\r\n        <div className=\"w-full flex-1 p-4 flex items-start justify-center border-b\">\r\n          <Image\r\n            src={`data:image/png;base64,${imgInBase64}`}\r\n            alt=\"result\"\r\n            width={600}\r\n            height={400}\r\n          />\r\n        </div>\r\n        <LogsOutput stdout={stdout} stderr={stderr} />\r\n      </div>\r\n    )\r\n  }\r\n\r\n  // No cell results, but there is stdout or stderr\r\n  if (stdout.length > 0 || stderr.length > 0) {\r\n    return <LogsOutput stdout={stdout} stderr={stderr} />\r\n  }\r\n\r\n  return <span>No output or logs</span>\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;AAKA,SAAS,WAAW,EAClB,MAAM,EACN,MAAM,EAIP;IACC,IAAI,OAAO,MAAM,KAAK,KAAK,OAAO,MAAM,KAAK,GAAG,OAAO;IAEvD,qBACE,wPAAC;QAAI,WAAU;;YACZ,UACC,OAAO,MAAM,GAAG,KAChB,OAAO,GAAG,CAAC,CAAC,KAAa,sBACvB,wPAAC;oBAAgB,WAAU;8BACxB;mBADO;;;;;YAIb,UACC,OAAO,MAAM,GAAG,KAChB,OAAO,GAAG,CAAC,CAAC,KAAa,sBACvB,wPAAC;oBAAgB,WAAU;8BACxB;mBADO;;;;;;;;;;;AAMpB;AAEO,SAAS,oBAAoB,EAClC,MAAM,EAGP;IACC,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,GAAG;IAEtD,kDAAkD;IAClD,IAAI,cAAc;QAChB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG;QACnC,qBACE,wPAAC;YAAI,WAAU;sBACb,cAAA,wPAAC,0HAAA,CAAA,QAAK;gBAAC,SAAQ;;kCACb,wPAAC,0MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,wPAAC,0HAAA,CAAA,aAAU;;4BACR;4BAAK;4BAAG;;;;;;;kCAEX,wPAAC,0HAAA,CAAA,mBAAgB;wBAAC,WAAU;kCACzB;;;;;;;;;;;;;;;;;IAKX;IAEA,4EAA4E;IAC5E,yBAAyB;IACzB,uCAAuC;IACvC,IAAI,YAAY,MAAM,GAAG,GAAG;QAC1B,MAAM,cAAc,WAAW,CAAC,EAAE,CAAC,GAAG;QACtC,qBACE,wPAAC;YAAI,WAAU;;8BACb,wPAAC;oBAAI,WAAU;8BACb,cAAA,wPAAC,6HAAA,CAAA,UAAK;wBACJ,KAAK,CAAC,sBAAsB,EAAE,YAAY,CAAC;wBAC3C,KAAI;wBACJ,OAAO;wBACP,QAAQ;;;;;;;;;;;8BAGZ,wPAAC;oBAAW,QAAQ;oBAAQ,QAAQ;;;;;;;;;;;;IAG1C;IAEA,iDAAiD;IACjD,IAAI,OAAO,MAAM,GAAG,KAAK,OAAO,MAAM,GAAG,GAAG;QAC1C,qBAAO,wPAAC;YAAW,QAAQ;YAAQ,QAAQ;;;;;;IAC7C;IAEA,qBAAO,wPAAC;kBAAK;;;;;;AACf"}}, {"offset": {"line": 4453, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4458, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/components/fragment-web.tsx"], "sourcesContent": ["import { Copy<PERSON>utton } from './ui/copy-button'\r\nimport { Button } from '@/components/ui/button'\r\nimport {\r\n  Toolt<PERSON>,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger,\r\n} from '@/components/ui/tooltip'\r\nimport { ExecutionResultWeb } from '@/lib/types'\r\nimport { RotateCw } from 'lucide-react'\r\nimport { useState } from 'react'\r\n\r\nexport function FragmentWeb({ result }: { result: ExecutionResultWeb }) {\r\n  const [iframeKey, setIframeKey] = useState(0)\r\n  if (!result) return null\r\n\r\n  function refreshIframe() {\r\n    setIframeKey((prevKey) => prevKey + 1)\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex flex-col w-full h-full\">\r\n      <iframe\r\n        key={iframeKey}\r\n        className=\"h-full w-full\"\r\n        sandbox=\"allow-forms allow-scripts allow-same-origin\"\r\n        loading=\"lazy\"\r\n        src={result.url}\r\n      />\r\n      <div className=\"p-2 border-t\">\r\n        <div className=\"flex items-center bg-muted dark:bg-white/10 rounded-2xl\">\r\n          <TooltipProvider>\r\n            <Tooltip delayDuration={0}>\r\n              <TooltipTrigger asChild>\r\n                <Button\r\n                  variant=\"link\"\r\n                  className=\"text-muted-foreground\"\r\n                  onClick={refreshIframe}\r\n                >\r\n                  <RotateCw className=\"h-4 w-4\" />\r\n                </Button>\r\n              </TooltipTrigger>\r\n              <TooltipContent>Refresh</TooltipContent>\r\n            </Tooltip>\r\n          </TooltipProvider>\r\n          <span className=\"text-muted-foreground text-xs flex-1 text-ellipsis overflow-hidden whitespace-nowrap\">\r\n            {result.url}\r\n          </span>\r\n          <TooltipProvider>\r\n            <Tooltip delayDuration={0}>\r\n              <TooltipTrigger asChild>\r\n                <CopyButton\r\n                  variant=\"link\"\r\n                  content={result.url}\r\n                  className=\"text-muted-foreground\"\r\n                />\r\n              </TooltipTrigger>\r\n              <TooltipContent>Copy URL</TooltipContent>\r\n            </Tooltip>\r\n          </TooltipProvider>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAYO,SAAS,YAAY,EAAE,MAAM,EAAkC;IACpE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,IAAI,CAAC,QAAQ,OAAO;IAEpB,SAAS;QACP,aAAa,CAAC,UAAY,UAAU;IACtC;IAEA,qBACE,wPAAC;QAAI,WAAU;;0BACb,wPAAC;gBAEC,WAAU;gBACV,SAAQ;gBACR,SAAQ;gBACR,KAAK,OAAO,GAAG;eAJV;;;;;0BAMP,wPAAC;gBAAI,WAAU;0BACb,cAAA,wPAAC;oBAAI,WAAU;;sCACb,wPAAC,4HAAA,CAAA,kBAAe;sCACd,cAAA,wPAAC,4HAAA,CAAA,UAAO;gCAAC,eAAe;;kDACtB,wPAAC,4HAAA,CAAA,iBAAc;wCAAC,OAAO;kDACrB,cAAA,wPAAC,2HAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,WAAU;4CACV,SAAS;sDAET,cAAA,wPAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAGxB,wPAAC,4HAAA,CAAA,iBAAc;kDAAC;;;;;;;;;;;;;;;;;sCAGpB,wPAAC;4BAAK,WAAU;sCACb,OAAO,GAAG;;;;;;sCAEb,wPAAC,4HAAA,CAAA,kBAAe;sCACd,cAAA,wPAAC,4HAAA,CAAA,UAAO;gCAAC,eAAe;;kDACtB,wPAAC,4HAAA,CAAA,iBAAc;wCAAC,OAAO;kDACrB,cAAA,wPAAC,mIAAA,CAAA,aAAU;4CACT,SAAQ;4CACR,SAAS,OAAO,GAAG;4CACnB,WAAU;;;;;;;;;;;kDAGd,wPAAC,4HAAA,CAAA,iBAAc;kDAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO9B"}}, {"offset": {"line": 4607, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4612, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/components/fragment-preview.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { FragmentInterpreter } from './fragment-interpreter'\r\nimport { FragmentWeb } from './fragment-web'\r\nimport { ExecutionResult } from '@/lib/types'\r\n\r\nexport function FragmentPreview({ result }: { result: ExecutionResult }) {\r\n  if (result.template === 'code-interpreter-v1') {\r\n    return <FragmentInterpreter result={result} />\r\n  }\r\n\r\n  return <FragmentWeb result={result} />\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAAA;;;;AAMO,SAAS,gBAAgB,EAAE,MAAM,EAA+B;IACrE,IAAI,OAAO,QAAQ,KAAK,uBAAuB;QAC7C,qBAAO,wPAAC,sIAAA,CAAA,sBAAmB;YAAC,QAAQ;;;;;;IACtC;IAEA,qBAAO,wPAAC,8HAAA,CAAA,cAAW;QAAC,QAAQ;;;;;;AAC9B"}}, {"offset": {"line": 4641, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4646, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Tabs = TabsPrimitive.Root\r\n\r\nconst TabsList = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.List>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.List\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex h-9 items-center justify-center rounded-lg bg-muted p-1 text-muted-foreground\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsList.displayName = TabsPrimitive.List.displayName\r\n\r\nconst TabsTrigger = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\r\n\r\nconst TabsContent = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Content\r\n    ref={ref}\r\n    className={cn(\r\n      \"ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsContent.displayName = TabsPrimitive.Content.displayName\r\n\r\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;;;;AAOA,MAAM,OAAO,iKAAc,IAAI;AAE/B,MAAM,yBAAW,gNAAM,UAAU,CAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,wPAAC,iKAAc,IAAI;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,6FACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG,iKAAc,IAAI,CAAC,WAAW;AAErD,MAAM,4BAAc,gNAAM,UAAU,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,wPAAC,iKAAc,OAAO;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kYACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,iKAAc,OAAO,CAAC,WAAW;AAE3D,MAAM,4BAAc,gNAAM,UAAU,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,wPAAC,iKAAc,OAAO;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,8HACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,iKAAc,OAAO,CAAC,WAAW"}}, {"offset": {"line": 4694, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4699, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/components/preview.tsx"], "sourcesContent": ["import { DeployDialog } from './deploy-dialog'\r\nimport { FragmentCode } from './fragment-code'\r\nimport { FragmentPreview } from './fragment-preview'\r\nimport { Button } from '@/components/ui/button'\r\nimport { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'\r\nimport {\r\n  Tooltip,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger,\r\n} from '@/components/ui/tooltip'\r\nimport { FragmentSchema } from '@/lib/schema'\r\nimport { ExecutionResult } from '@/lib/types'\r\nimport { DeepPartial } from 'ai'\r\nimport { ChevronsRight, LoaderCircle } from 'lucide-react'\r\nimport { Dispatch, SetStateAction } from 'react'\r\n\r\nexport function Preview({\r\n  teamID,\r\n  accessToken,\r\n  selectedTab,\r\n  onSelectedTabChange,\r\n  isChatLoading,\r\n  isPreviewLoading,\r\n  fragment,\r\n  result,\r\n  onClose,\r\n}: {\r\n  teamID: string | undefined\r\n  accessToken: string | undefined\r\n  selectedTab: 'code' | 'fragment'\r\n  onSelectedTabChange: Dispatch<SetStateAction<'code' | 'fragment'>>\r\n  isChatLoading: boolean\r\n  isPreviewLoading: boolean\r\n  fragment?: DeepPartial<FragmentSchema>\r\n  result?: ExecutionResult\r\n  onClose: () => void\r\n}) {\r\n  if (!fragment) {\r\n    return null\r\n  }\r\n\r\n  const isLinkAvailable = result?.template !== 'code-interpreter-v1'\r\n\r\n  return (\r\n    <div className=\"absolute md:relative z-10 top-0 left-0 shadow-2xl md:rounded-tl-3xl md:rounded-bl-3xl md:border-l md:border-y bg-popover h-full w-full overflow-auto\">\r\n      <Tabs\r\n        value={selectedTab}\r\n        onValueChange={(value) =>\r\n          onSelectedTabChange(value as 'code' | 'fragment')\r\n        }\r\n        className=\"h-full flex flex-col items-start justify-start\"\r\n      >\r\n        <div className=\"w-full p-2 grid grid-cols-3 items-center border-b\">\r\n          <TooltipProvider>\r\n            <Tooltip delayDuration={0}>\r\n              <TooltipTrigger asChild>\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  size=\"icon\"\r\n                  className=\"text-muted-foreground\"\r\n                  onClick={onClose}\r\n                >\r\n                  <ChevronsRight className=\"h-5 w-5\" />\r\n                </Button>\r\n              </TooltipTrigger>\r\n              <TooltipContent>Close sidebar</TooltipContent>\r\n            </Tooltip>\r\n          </TooltipProvider>\r\n          <div className=\"flex justify-center\">\r\n            <TabsList className=\"px-1 py-0 border h-8\">\r\n              <TabsTrigger\r\n                className=\"font-normal text-xs py-1 px-2 gap-1 flex items-center\"\r\n                value=\"code\"\r\n              >\r\n                {isChatLoading && (\r\n                  <LoaderCircle\r\n                    strokeWidth={3}\r\n                    className=\"h-3 w-3 animate-spin\"\r\n                  />\r\n                )}\r\n                Code\r\n              </TabsTrigger>\r\n              <TabsTrigger\r\n                disabled={!result}\r\n                className=\"font-normal text-xs py-1 px-2 gap-1 flex items-center\"\r\n                value=\"fragment\"\r\n              >\r\n                Preview\r\n                {isPreviewLoading && (\r\n                  <LoaderCircle\r\n                    strokeWidth={3}\r\n                    className=\"h-3 w-3 animate-spin\"\r\n                  />\r\n                )}\r\n              </TabsTrigger>\r\n            </TabsList>\r\n          </div>\r\n          {result && (\r\n            <div className=\"flex items-center justify-end gap-2\">\r\n              {isLinkAvailable && (\r\n                <DeployDialog\r\n                  url={result.url!}\r\n                  sbxId={result.sbxId!}\r\n                  teamID={teamID}\r\n                  accessToken={accessToken}\r\n                />\r\n              )}\r\n            </div>\r\n          )}\r\n        </div>\r\n        {fragment && (\r\n          <div className=\"overflow-y-auto w-full h-full\">\r\n            <TabsContent value=\"code\" className=\"h-full\">\r\n              {fragment.code && fragment.file_path && (\r\n                <FragmentCode\r\n                  files={[\r\n                    {\r\n                      name: fragment.file_path,\r\n                      content: fragment.code,\r\n                    },\r\n                  ]}\r\n                />\r\n              )}\r\n            </TabsContent>\r\n            <TabsContent value=\"fragment\" className=\"h-full\">\r\n              {result && <FragmentPreview result={result as ExecutionResult} />}\r\n            </TabsContent>\r\n          </div>\r\n        )}\r\n      </Tabs>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAiBO,SAAS,QAAQ,EACtB,MAAM,EACN,WAAW,EACX,WAAW,EACX,mBAAmB,EACnB,aAAa,EACb,gBAAgB,EAChB,QAAQ,EACR,MAAM,EACN,OAAO,EAWR;IACC,IAAI,CAAC,UAAU;QACb,OAAO;IACT;IAEA,MAAM,kBAAkB,QAAQ,aAAa;IAE7C,qBACE,wPAAC;QAAI,WAAU;kBACb,cAAA,wPAAC,yHAAA,CAAA,OAAI;YACH,OAAO;YACP,eAAe,CAAC,QACd,oBAAoB;YAEtB,WAAU;;8BAEV,wPAAC;oBAAI,WAAU;;sCACb,wPAAC,4HAAA,CAAA,kBAAe;sCACd,cAAA,wPAAC,4HAAA,CAAA,UAAO;gCAAC,eAAe;;kDACtB,wPAAC,4HAAA,CAAA,iBAAc;wCAAC,OAAO;kDACrB,cAAA,wPAAC,2HAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;4CACV,SAAS;sDAET,cAAA,wPAAC,wNAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAG7B,wPAAC,4HAAA,CAAA,iBAAc;kDAAC;;;;;;;;;;;;;;;;;sCAGpB,wPAAC;4BAAI,WAAU;sCACb,cAAA,wPAAC,yHAAA,CAAA,WAAQ;gCAAC,WAAU;;kDAClB,wPAAC,yHAAA,CAAA,cAAW;wCACV,WAAU;wCACV,OAAM;;4CAEL,+BACC,wPAAC,sNAAA,CAAA,eAAY;gDACX,aAAa;gDACb,WAAU;;;;;;4CAEZ;;;;;;;kDAGJ,wPAAC,yHAAA,CAAA,cAAW;wCACV,UAAU,CAAC;wCACX,WAAU;wCACV,OAAM;;4CACP;4CAEE,kCACC,wPAAC,sNAAA,CAAA,eAAY;gDACX,aAAa;gDACb,WAAU;;;;;;;;;;;;;;;;;;;;;;;wBAMnB,wBACC,wPAAC;4BAAI,WAAU;sCACZ,iCACC,wPAAC,+HAAA,CAAA,eAAY;gCACX,KAAK,OAAO,GAAG;gCACf,OAAO,OAAO,KAAK;gCACnB,QAAQ;gCACR,aAAa;;;;;;;;;;;;;;;;;gBAMtB,0BACC,wPAAC;oBAAI,WAAU;;sCACb,wPAAC,yHAAA,CAAA,cAAW;4BAAC,OAAM;4BAAO,WAAU;sCACjC,SAAS,IAAI,IAAI,SAAS,SAAS,kBAClC,wPAAC,+HAAA,CAAA,eAAY;gCACX,OAAO;oCACL;wCACE,MAAM,SAAS,SAAS;wCACxB,SAAS,SAAS,IAAI;oCACxB;iCACD;;;;;;;;;;;sCAIP,wPAAC,yHAAA,CAAA,cAAW;4BAAC,OAAM;4BAAW,WAAU;sCACrC,wBAAU,wPAAC,kIAAA,CAAA,kBAAe;gCAAC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlD"}}, {"offset": {"line": 4915, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4920, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\r\n\r\nexport const supabase = process.env.NEXT_PUBLIC_ENABLE_SUPABASE\r\n  ? createClient(\r\n      process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\r\n    )\r\n  : undefined\r\n"], "names": [], "mappings": ";;;;;;AAEO,MAAM,WAAW,QAAQ,GAAG,CAAC,2BAA2B,GAC3D,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EACT,QAAQ,GAAG,CAAC,wBAAwB,EACpC,QAAQ,GAAG,CAAC,6BAA6B,IAE3C"}}, {"offset": {"line": 4927, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4932, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/lib/auth.ts"], "sourcesContent": ["import { supabase } from './supabase'\r\nimport { ViewType } from '@/components/auth'\r\nimport { Session } from '@supabase/supabase-js'\r\nimport { usePostHog } from 'posthog-js/react'\r\nimport { useState, useEffect } from 'react'\r\n\r\ntype UserTeam = {\r\n  email: string\r\n  id: string\r\n  name: string\r\n  tier: string\r\n}\r\n\r\nexport async function getUserTeam(\r\n  session: Session,\r\n): Promise<UserTeam | undefined> {\r\n  const { data: defaultTeam } = await supabase!\r\n    .from('users_teams')\r\n    .select('teams (id, name, tier, email)')\r\n    .eq('user_id', session?.user.id)\r\n    .eq('is_default', true)\r\n    .single()\r\n\r\n  return defaultTeam?.teams as unknown as UserTeam\r\n}\r\n\r\nexport function useAuth(\r\n  setAuthDialog: (value: boolean) => void,\r\n  setAuthView: (value: ViewType) => void,\r\n) {\r\n  const [session, setSession] = useState<Session | null>(null)\r\n  const [userTeam, setUserTeam] = useState<UserTeam | undefined>(undefined)\r\n  const [recovery, setRecovery] = useState(false)\r\n  const posthog = usePostHog()\r\n\r\n  useEffect(() => {\r\n    if (!supabase) {\r\n      console.warn('Supabase is not initialized')\r\n      return setSession({ user: { email: '<EMAIL>' } } as Session)\r\n    }\r\n\r\n    supabase.auth.getSession().then(({ data: { session } }) => {\r\n      setSession(session)\r\n      if (session) {\r\n        getUserTeam(session).then(setUserTeam)\r\n        if (!session.user.user_metadata.is_fragments_user) {\r\n          supabase?.auth.updateUser({\r\n            data: { is_fragments_user: true },\r\n          })\r\n        }\r\n        posthog.identify(session?.user.id, {\r\n          email: session?.user.email,\r\n          supabase_id: session?.user.id,\r\n        })\r\n        posthog.capture('sign_in')\r\n      }\r\n    })\r\n\r\n    const {\r\n      data: { subscription },\r\n    } = supabase.auth.onAuthStateChange((_event, session) => {\r\n      setSession(session)\r\n\r\n      if (_event === 'PASSWORD_RECOVERY') {\r\n        setRecovery(true)\r\n        setAuthView('update_password')\r\n        setAuthDialog(true)\r\n      }\r\n\r\n      if (_event === 'USER_UPDATED' && recovery) {\r\n        setRecovery(false)\r\n      }\r\n\r\n      if (_event === 'SIGNED_IN' && !recovery) {\r\n        getUserTeam(session as Session).then(setUserTeam)\r\n        setAuthDialog(false)\r\n        if (!session?.user.user_metadata.is_fragments_user) {\r\n          supabase?.auth.updateUser({\r\n            data: { is_fragments_user: true },\r\n          })\r\n        }\r\n        posthog.identify(session?.user.id, {\r\n          email: session?.user.email,\r\n          supabase_id: session?.user.id,\r\n        })\r\n        posthog.capture('sign_in')\r\n      }\r\n\r\n      if (_event === 'SIGNED_OUT') {\r\n        setAuthView('sign_in')\r\n        posthog.capture('sign_out')\r\n        posthog.reset()\r\n        setRecovery(false)\r\n      }\r\n    })\r\n\r\n    return () => subscription.unsubscribe()\r\n  }, [recovery, setAuthDialog, setAuthView, posthog])\r\n\r\n  return {\r\n    session,\r\n    userTeam,\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAaO,eAAe,YACpB,OAAgB;IAEhB,MAAM,EAAE,MAAM,WAAW,EAAE,GAAG,MAAM,+GAAA,CAAA,WAAQ,CACzC,IAAI,CAAC,eACL,MAAM,CAAC,iCACP,EAAE,CAAC,WAAW,SAAS,KAAK,IAC5B,EAAE,CAAC,cAAc,MACjB,MAAM;IAET,OAAO,aAAa;AACtB;AAEO,SAAS,QACd,aAAuC,EACvC,WAAsC;IAEtC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAwB;IAC/D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,UAAU,CAAA,GAAA,8JAAA,CAAA,aAAU,AAAD;IAEzB,CAAA,GAAA,+MAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,+GAAA,CAAA,WAAQ,EAAE;YACb,QAAQ,IAAI,CAAC;YACb,OAAO,WAAW;gBAAE,MAAM;oBAAE,OAAO;gBAAe;YAAE;QACtD;QAEA,+GAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE;YACpD,WAAW;YACX,IAAI,SAAS;gBACX,YAAY,SAAS,IAAI,CAAC;gBAC1B,IAAI,CAAC,QAAQ,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE;oBACjD,+GAAA,CAAA,WAAQ,EAAE,KAAK,WAAW;wBACxB,MAAM;4BAAE,mBAAmB;wBAAK;oBAClC;gBACF;gBACA,QAAQ,QAAQ,CAAC,SAAS,KAAK,IAAI;oBACjC,OAAO,SAAS,KAAK;oBACrB,aAAa,SAAS,KAAK;gBAC7B;gBACA,QAAQ,OAAO,CAAC;YAClB;QACF;QAEA,MAAM,EACJ,MAAM,EAAE,YAAY,EAAE,EACvB,GAAG,+GAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,QAAQ;YAC3C,WAAW;YAEX,IAAI,WAAW,qBAAqB;gBAClC,YAAY;gBACZ,YAAY;gBACZ,cAAc;YAChB;YAEA,IAAI,WAAW,kBAAkB,UAAU;gBACzC,YAAY;YACd;YAEA,IAAI,WAAW,eAAe,CAAC,UAAU;gBACvC,YAAY,SAAoB,IAAI,CAAC;gBACrC,cAAc;gBACd,IAAI,CAAC,SAAS,KAAK,cAAc,mBAAmB;oBAClD,+GAAA,CAAA,WAAQ,EAAE,KAAK,WAAW;wBACxB,MAAM;4BAAE,mBAAmB;wBAAK;oBAClC;gBACF;gBACA,QAAQ,QAAQ,CAAC,SAAS,KAAK,IAAI;oBACjC,OAAO,SAAS,KAAK;oBACrB,aAAa,SAAS,KAAK;gBAC7B;gBACA,QAAQ,OAAO,CAAC;YAClB;YAEA,IAAI,WAAW,cAAc;gBAC3B,YAAY;gBACZ,QAAQ,OAAO,CAAC;gBAChB,QAAQ,KAAK;gBACb,YAAY;YACd;QACF;QAEA,OAAO,IAAM,aAAa,WAAW;IACvC,GAAG;QAAC;QAAU;QAAe;QAAa;KAAQ;IAElD,OAAO;QACL;QACA;IACF;AACF"}}, {"offset": {"line": 5024, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5029, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/lib/messages.ts"], "sourcesContent": ["import { FragmentSchema } from './schema'\r\nimport { ExecutionResult } from './types'\r\nimport { DeepPartial } from 'ai'\r\n\r\nexport type MessageText = {\r\n  type: 'text'\r\n  text: string\r\n}\r\n\r\nexport type MessageCode = {\r\n  type: 'code'\r\n  text: string\r\n}\r\n\r\nexport type MessageImage = {\r\n  type: 'image'\r\n  image: string\r\n}\r\n\r\nexport type Message = {\r\n  role: 'assistant' | 'user'\r\n  content: Array<MessageText | MessageCode | MessageImage>\r\n  object?: DeepPartial<FragmentSchema>\r\n  result?: ExecutionResult\r\n}\r\n\r\nexport function toAISDKMessages(messages: Message[]) {\r\n  return messages.map((message) => ({\r\n    role: message.role,\r\n    content: message.content.map((content) => {\r\n      if (content.type === 'code') {\r\n        return {\r\n          type: 'text',\r\n          text: content.text,\r\n        }\r\n      }\r\n\r\n      return content\r\n    }),\r\n  }))\r\n}\r\n\r\nexport async function toMessageImage(files: File[]) {\r\n  if (files.length === 0) {\r\n    return []\r\n  }\r\n\r\n  return Promise.all(\r\n    files.map(async (file) => {\r\n      const base64 = Buffer.from(await file.arrayBuffer()).toString('base64')\r\n      return `data:${file.type};base64,${base64}`\r\n    }),\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AA0BO,SAAS,gBAAgB,QAAmB;IACjD,OAAO,SAAS,GAAG,CAAC,CAAC,UAAY,CAAC;YAChC,MAAM,QAAQ,IAAI;YAClB,SAAS,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC;gBAC5B,IAAI,QAAQ,IAAI,KAAK,QAAQ;oBAC3B,OAAO;wBACL,MAAM;wBACN,MAAM,QAAQ,IAAI;oBACpB;gBACF;gBAEA,OAAO;YACT;QACF,CAAC;AACH;AAEO,eAAe,eAAe,KAAa;IAChD,IAAI,MAAM,MAAM,KAAK,GAAG;QACtB,OAAO,EAAE;IACX;IAEA,OAAO,QAAQ,GAAG,CAChB,MAAM,GAAG,CAAC,OAAO;QACf,MAAM,SAAS,OAAO,IAAI,CAAC,MAAM,KAAK,WAAW,IAAI,QAAQ,CAAC;QAC9D,OAAO,CAAC,KAAK,EAAE,KAAK,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC;IAC7C;AAEJ"}}, {"offset": {"line": 5056, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5065, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/lib/schema.ts"], "sourcesContent": ["import { z } from 'zod'\r\n\r\nexport const fragmentSchema = z.object({\r\n  commentary: z.string().describe(`Describe what you're about to do and the steps you want to take for generating the fragment in great detail.`),\r\n  template: z.string().describe('Name of the template used to generate the fragment.'),\r\n  // template_ready: z.boolean().describe('Detect if finished identifying the template.'),\r\n  title: z.string().describe('Short title of the fragment. Max 3 words.'),\r\n  description: z.string().describe('Short description of the fragment. Max 1 sentence.'),\r\n  additional_dependencies: z.array(z.string()).describe('Additional dependencies required by the fragment. Do not include dependencies that are already included in the template.'),\r\n  has_additional_dependencies: z.boolean().describe('Detect if additional dependencies that are not included in the template are required by the fragment.'),\r\n  install_dependencies_command: z.string().describe('Command to install additional dependencies required by the fragment.'),\r\n  // install_dependencies_ready: z.boolean().describe('Detect if finished identifying additional dependencies.'),\r\n  port: z.number().nullable().describe('Port number used by the resulted fragment. Null when no ports are exposed.'),\r\n  file_path: z.string().describe('Relative path to the file, including the file name.'),\r\n  code: z.string().describe('Code generated by the fragment. Only runnable code is allowed.'),\r\n  // code: z.array(z.object({\r\n  //   file_name: z.string().describe('Name of the file.'),\r\n  //   file_path: z.string().describe('Relative path to the file, including the file name.'),\r\n  //   file_content: z.string().describe('Content of the file.'),\r\n  //   file_finished: z.boolean().describe('Detect if finished generating the file.'),\r\n  // })),\r\n  // code_finished: z.boolean().describe('Detect if finished generating the code.'),\r\n  // error: z.string().optional().describe('Error message if the fragment is not valid.'),\r\n})\r\n\r\nexport type FragmentSchema = z.infer<typeof fragmentSchema>\r\n"], "names": [], "mappings": ";;;;;;AAEO,MAAM,iBAAiB,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACrC,YAAY,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC,CAAC,4GAA4G,CAAC;IAC9I,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC9B,wFAAwF;IACxF,OAAO,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC3B,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACjC,yBAAyB,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;IACtD,6BAA6B,oIAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ,CAAC;IAClD,8BAA8B,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAClD,+GAA+G;IAC/G,MAAM,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;IACrC,WAAW,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC/B,MAAM,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;AAS5B"}}, {"offset": {"line": 5085, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5094, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/lib/templates.ts"], "sourcesContent": ["import templates from './templates.json'\r\n\r\nexport default templates\r\nexport type Templates = typeof templates\r\nexport type TemplateId = keyof typeof templates\r\nexport type TemplateConfig = typeof templates[TemplateId]\r\n\r\nexport function templatesToPrompt(templates: Templates) {\r\n  return `${Object.entries(templates).map(([id, t], index) => `${index + 1}. ${id}: \"${t.instructions}\". File: ${t.file || 'none'}. Dependencies installed: ${t.lib.join(', ')}. Port: ${t.port || 'none'}.`).join('\\n')}`\r\n}\r\n"], "names": [], "mappings": ";;;;;;;uCAEe,wFAAA,CAAA,UAAS;AAKjB,SAAS,kBAAkB,SAAoB;IACpD,OAAO,CAAC,EAAE,OAAO,OAAO,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,EAAE,QAAU,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,YAAY,CAAC,SAAS,EAAE,EAAE,IAAI,IAAI,OAAO,0BAA0B,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,QAAQ,EAAE,EAAE,IAAI,IAAI,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC;AAC1N"}}, {"offset": {"line": 5105, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5110, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/app/page.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { ViewType } from '@/components/auth'\r\nimport { AuthDialog } from '@/components/auth-dialog'\r\nimport { Chat } from '@/components/chat'\r\nimport { ChatInput } from '@/components/chat-input'\r\nimport { ChatPicker } from '@/components/chat-picker'\r\nimport { ChatSettings } from '@/components/chat-settings'\r\nimport { ClientOnly } from '@/components/client-only'\r\nimport { NavBar } from '@/components/navbar'\r\nimport { Preview } from '@/components/preview'\r\nimport { useAuth } from '@/lib/auth'\r\nimport { Message, toAISDKMessages, toMessageImage } from '@/lib/messages'\r\nimport { LLMModelConfig } from '@/lib/models'\r\nimport modelsList from '@/lib/models.json'\r\nimport { FragmentSchema, fragmentSchema as schema } from '@/lib/schema'\r\nimport { supabase } from '@/lib/supabase'\r\nimport templates, { TemplateId } from '@/lib/templates'\r\nimport { ExecutionResult } from '@/lib/types'\r\nimport { DeepPartial } from 'ai'\r\nimport { experimental_useObject as useObject } from 'ai/react'\r\nimport { usePostHog } from 'posthog-js/react'\r\nimport { SetStateAction, useEffect, useState } from 'react'\r\nimport { useLocalStorage } from 'usehooks-ts'\r\n\r\nexport default function Home() {\r\n  const [mounted, setMounted] = useState(false)\r\n  const [chatInput, setChatInput] = useLocalStorage('chat', '')\r\n  const [files, setFiles] = useState<File[]>([])\r\n  const [selectedTemplate, setSelectedTemplate] = useState<'auto' | TemplateId>(\r\n    'auto',\r\n  )\r\n  const [languageModel, setLanguageModel] = useLocalStorage<LLMModelConfig>(\r\n    'languageModel',\r\n    {\r\n      model: 'claude-3-5-sonnet-latest',\r\n    },\r\n  )\r\n\r\n  useEffect(() => {\r\n    setMounted(true)\r\n  }, [])\r\n\r\n  const posthog = usePostHog()\r\n\r\n  const [result, setResult] = useState<ExecutionResult>()\r\n  const [messages, setMessages] = useState<Message[]>([])\r\n  const [fragment, setFragment] = useState<DeepPartial<FragmentSchema>>()\r\n  const [currentTab, setCurrentTab] = useState<'code' | 'fragment'>('code')\r\n  const [isPreviewLoading, setIsPreviewLoading] = useState(false)\r\n  const [isAuthDialogOpen, setAuthDialog] = useState(false)\r\n  const [authView, setAuthView] = useState<ViewType>('sign_in')\r\n  const [isRateLimited, setIsRateLimited] = useState(false)\r\n  const [errorMessage, setErrorMessage] = useState('')\r\n  const { session, userTeam } = useAuth(setAuthDialog, setAuthView)\r\n\r\n  const filteredModels = mounted ? modelsList.models.filter((model) => {\r\n    if (process.env.NEXT_PUBLIC_HIDE_LOCAL_MODELS) {\r\n      return model.providerId !== 'ollama'\r\n    }\r\n    return true\r\n  }) : modelsList.models\r\n\r\n  const currentModel = filteredModels.find(\r\n    (model) => model.id === languageModel.model,\r\n  )\r\n  const currentTemplate =\r\n    selectedTemplate === 'auto'\r\n      ? templates\r\n      : { [selectedTemplate]: templates[selectedTemplate] }\r\n  const lastMessage = messages[messages.length - 1]\r\n\r\n  const { object, submit, isLoading, stop, error } = useObject({\r\n    api: '/api/chat',\r\n    schema,\r\n    onError: (error) => {\r\n      console.error('Error submitting request:', error)\r\n      if (error.message.includes('limit')) {\r\n        setIsRateLimited(true)\r\n      }\r\n\r\n      setErrorMessage(error.message)\r\n    },\r\n    onFinish: async ({ object: fragment, error }) => {\r\n      if (!error) {\r\n        // send it to /api/sandbox\r\n        console.log('fragment', fragment)\r\n        setIsPreviewLoading(true)\r\n        posthog.capture('fragment_generated', {\r\n          template: fragment?.template,\r\n        })\r\n\r\n        const response = await fetch('/api/sandbox', {\r\n          method: 'POST',\r\n          body: JSON.stringify({\r\n            fragment,\r\n            userID: session?.user?.id,\r\n            teamID: userTeam?.id,\r\n            accessToken: session?.access_token,\r\n          }),\r\n        })\r\n\r\n        const result = await response.json()\r\n        console.log('result', result)\r\n        posthog.capture('sandbox_created', { url: result.url })\r\n\r\n        setResult(result)\r\n        setCurrentPreview({ fragment, result })\r\n        setMessage({ result })\r\n        setCurrentTab('fragment')\r\n        setIsPreviewLoading(false)\r\n      }\r\n    },\r\n  })\r\n\r\n  useEffect(() => {\r\n    if (object) {\r\n      setFragment(object)\r\n      const content: Message['content'] = [\r\n        { type: 'text', text: object.commentary || '' },\r\n        { type: 'code', text: object.code || '' },\r\n      ]\r\n\r\n      if (!lastMessage || lastMessage.role !== 'assistant') {\r\n        addMessage({\r\n          role: 'assistant',\r\n          content,\r\n          object,\r\n        })\r\n      }\r\n\r\n      if (lastMessage && lastMessage.role === 'assistant') {\r\n        setMessage({\r\n          content,\r\n          object,\r\n        })\r\n      }\r\n    }\r\n  }, [object])\r\n\r\n  useEffect(() => {\r\n    if (error) stop()\r\n  }, [error])\r\n\r\n  function setMessage(message: Partial<Message>, index?: number) {\r\n    setMessages((previousMessages) => {\r\n      const updatedMessages = [...previousMessages]\r\n      updatedMessages[index ?? previousMessages.length - 1] = {\r\n        ...previousMessages[index ?? previousMessages.length - 1],\r\n        ...message,\r\n      }\r\n\r\n      return updatedMessages\r\n    })\r\n  }\r\n\r\n  async function handleSubmitAuth(e: React.FormEvent<HTMLFormElement>) {\r\n    e.preventDefault()\r\n\r\n    if (!session) {\r\n      return setAuthDialog(true)\r\n    }\r\n\r\n    if (isLoading) {\r\n      stop()\r\n    }\r\n\r\n    const content: Message['content'] = [{ type: 'text', text: chatInput }]\r\n    const images = await toMessageImage(files)\r\n\r\n    if (images.length > 0) {\r\n      images.forEach((image) => {\r\n        content.push({ type: 'image', image })\r\n      })\r\n    }\r\n\r\n    const updatedMessages = addMessage({\r\n      role: 'user',\r\n      content,\r\n    })\r\n\r\n    submit({\r\n      userID: session?.user?.id,\r\n      teamID: userTeam?.id,\r\n      messages: toAISDKMessages(updatedMessages),\r\n      template: currentTemplate,\r\n      model: currentModel,\r\n      config: languageModel,\r\n    })\r\n\r\n    setChatInput('')\r\n    setFiles([])\r\n    setCurrentTab('code')\r\n\r\n    posthog.capture('chat_submit', {\r\n      template: selectedTemplate,\r\n      model: languageModel.model,\r\n    })\r\n  }\r\n\r\n  function retry() {\r\n    submit({\r\n      userID: session?.user?.id,\r\n      teamID: userTeam?.id,\r\n      messages: toAISDKMessages(messages),\r\n      template: currentTemplate,\r\n      model: currentModel,\r\n      config: languageModel,\r\n    })\r\n  }\r\n\r\n  function addMessage(message: Message) {\r\n    setMessages((previousMessages) => [...previousMessages, message])\r\n    return [...messages, message]\r\n  }\r\n\r\n  function handleSaveInputChange(e: React.ChangeEvent<HTMLTextAreaElement>) {\r\n    setChatInput(e.target.value)\r\n  }\r\n\r\n  function handleFileChange(change: SetStateAction<File[]>) {\r\n    setFiles(change)\r\n  }\r\n\r\n  function logout() {\r\n    supabase\r\n      ? supabase.auth.signOut()\r\n      : console.warn('Supabase is not initialized')\r\n  }\r\n\r\n  function handleLanguageModelChange(e: LLMModelConfig) {\r\n    setLanguageModel({ ...languageModel, ...e })\r\n  }\r\n\r\n  function handleSocialClick(target: 'github' | 'x' | 'discord') {\r\n    if (target === 'github') {\r\n      window.open('https://github.com/e2b-dev/fragments', '_blank')\r\n    } else if (target === 'x') {\r\n      window.open('https://x.com/e2b_dev', '_blank')\r\n    } else if (target === 'discord') {\r\n      window.open('https://discord.gg/U7KEcGErtQ', '_blank')\r\n    }\r\n\r\n    posthog.capture(`${target}_click`)\r\n  }\r\n\r\n  function handleClearChat() {\r\n    stop()\r\n    setChatInput('')\r\n    setFiles([])\r\n    setMessages([])\r\n    setFragment(undefined)\r\n    setResult(undefined)\r\n    setCurrentTab('code')\r\n    setIsPreviewLoading(false)\r\n  }\r\n\r\n  function setCurrentPreview(preview: {\r\n    fragment: DeepPartial<FragmentSchema> | undefined\r\n    result: ExecutionResult | undefined\r\n  }) {\r\n    setFragment(preview.fragment)\r\n    setResult(preview.result)\r\n  }\r\n\r\n  function handleUndo() {\r\n    setMessages((previousMessages) => [...previousMessages.slice(0, -2)])\r\n    setCurrentPreview({ fragment: undefined, result: undefined })\r\n  }\r\n\r\n  if (!mounted) {\r\n    return (\r\n      <main className=\"flex min-h-screen max-h-screen\">\r\n        <div className=\"grid w-full md:grid-cols-2\">\r\n          <div className=\"flex flex-col w-full max-h-full max-w-[800px] mx-auto px-4 overflow-auto col-span-2\">\r\n            <div className=\"flex-1 flex items-center justify-center\">\r\n              <div className=\"animate-pulse\">Loading...</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </main>\r\n    )\r\n  }\r\n\r\n  return (\r\n    <main className=\"flex min-h-screen max-h-screen\">\r\n      {supabase && (\r\n        <AuthDialog\r\n          open={isAuthDialogOpen}\r\n          setOpen={setAuthDialog}\r\n          view={authView}\r\n          supabase={supabase}\r\n        />\r\n      )}\r\n      <div className=\"grid w-full md:grid-cols-2\">\r\n        <div\r\n          className={`flex flex-col w-full max-h-full max-w-[800px] mx-auto px-4 overflow-auto ${fragment ? 'col-span-1' : 'col-span-2'}`}\r\n        >\r\n          <NavBar\r\n            session={session}\r\n            showLogin={() => setAuthDialog(true)}\r\n            signOut={logout}\r\n            onSocialClick={handleSocialClick}\r\n            onClear={handleClearChat}\r\n            canClear={messages.length > 0}\r\n            canUndo={messages.length > 1 && !isLoading}\r\n            onUndo={handleUndo}\r\n          />\r\n          <Chat\r\n            messages={messages}\r\n            isLoading={isLoading}\r\n            setCurrentPreview={setCurrentPreview}\r\n          />\r\n          <ChatInput\r\n            retry={retry}\r\n            isErrored={error !== undefined}\r\n            errorMessage={errorMessage}\r\n            isLoading={isLoading}\r\n            isRateLimited={isRateLimited}\r\n            stop={stop}\r\n            input={chatInput}\r\n            handleInputChange={handleSaveInputChange}\r\n            handleSubmit={handleSubmitAuth}\r\n            isMultiModal={currentModel?.multiModal || false}\r\n            files={files}\r\n            handleFileChange={handleFileChange}\r\n          >\r\n            <ClientOnly>\r\n              <ChatPicker\r\n                templates={templates}\r\n                selectedTemplate={selectedTemplate}\r\n                onSelectedTemplateChange={setSelectedTemplate}\r\n                models={filteredModels}\r\n                languageModel={languageModel}\r\n                onLanguageModelChange={handleLanguageModelChange}\r\n              />\r\n              <ChatSettings\r\n                languageModel={languageModel}\r\n                onLanguageModelChange={handleLanguageModelChange}\r\n                apiKeyConfigurable={!process.env.NEXT_PUBLIC_NO_API_KEY_INPUT}\r\n                baseURLConfigurable={!process.env.NEXT_PUBLIC_NO_BASE_URL_INPUT}\r\n              />\r\n            </ClientOnly>\r\n          </ChatInput>\r\n        </div>\r\n        <Preview\r\n          teamID={userTeam?.id}\r\n          accessToken={session?.access_token}\r\n          selectedTab={currentTab}\r\n          onSelectedTabChange={setCurrentTab}\r\n          isChatLoading={isLoading}\r\n          isPreviewLoading={isPreviewLoading}\r\n          fragment={fragment}\r\n          result={result as ExecutionResult}\r\n          onClose={() => setFragment(undefined)}\r\n        />\r\n      </div>\r\n    </main>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;;;;;;;;;;;AAyBe,SAAS;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,+IAAA,CAAA,kBAAe,AAAD,EAAE,QAAQ;IAC1D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EACrD;IAEF,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,+IAAA,CAAA,kBAAe,AAAD,EACtD,iBACA;QACE,OAAO;IACT;IAGF,CAAA,GAAA,+MAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,MAAM,UAAU,CAAA,GAAA,8JAAA,CAAA,aAAU,AAAD;IAEzB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAuB;IAClE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,kBAAkB,cAAc,GAAG,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAY;IACnD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,2GAAA,CAAA,UAAO,AAAD,EAAE,eAAe;IAErD,MAAM,iBAAiB,UAAU,qFAAA,CAAA,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QACzD,IAAI,QAAQ,GAAG,CAAC,6BAA6B,EAAE;YAC7C,OAAO,MAAM,UAAU,KAAK;QAC9B;QACA,OAAO;IACT,KAAK,qFAAA,CAAA,UAAU,CAAC,MAAM;IAEtB,MAAM,eAAe,eAAe,IAAI,CACtC,CAAC,QAAU,MAAM,EAAE,KAAK,cAAc,KAAK;IAE7C,MAAM,kBACJ,qBAAqB,SACjB,gHAAA,CAAA,UAAS,GACT;QAAE,CAAC,iBAAiB,EAAE,gHAAA,CAAA,UAAS,CAAC,iBAAiB;IAAC;IACxD,MAAM,cAAc,QAAQ,CAAC,SAAS,MAAM,GAAG,EAAE;IAEjD,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,6IAAA,CAAA,yBAAS,AAAD,EAAE;QAC3D,KAAK;QACL,QAAA,6GAAA,CAAA,iBAAM;QACN,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,UAAU;gBACnC,iBAAiB;YACnB;YAEA,gBAAgB,MAAM,OAAO;QAC/B;QACA,UAAU,OAAO,EAAE,QAAQ,QAAQ,EAAE,KAAK,EAAE;YAC1C,IAAI,CAAC,OAAO;gBACV,0BAA0B;gBAC1B,QAAQ,GAAG,CAAC,YAAY;gBACxB,oBAAoB;gBACpB,QAAQ,OAAO,CAAC,sBAAsB;oBACpC,UAAU,UAAU;gBACtB;gBAEA,MAAM,WAAW,MAAM,MAAM,gBAAgB;oBAC3C,QAAQ;oBACR,MAAM,KAAK,SAAS,CAAC;wBACnB;wBACA,QAAQ,SAAS,MAAM;wBACvB,QAAQ,UAAU;wBAClB,aAAa,SAAS;oBACxB;gBACF;gBAEA,MAAM,SAAS,MAAM,SAAS,IAAI;gBAClC,QAAQ,GAAG,CAAC,UAAU;gBACtB,QAAQ,OAAO,CAAC,mBAAmB;oBAAE,KAAK,OAAO,GAAG;gBAAC;gBAErD,UAAU;gBACV,kBAAkB;oBAAE;oBAAU;gBAAO;gBACrC,WAAW;oBAAE;gBAAO;gBACpB,cAAc;gBACd,oBAAoB;YACtB;QACF;IACF;IAEA,CAAA,GAAA,+MAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV,YAAY;YACZ,MAAM,UAA8B;gBAClC;oBAAE,MAAM;oBAAQ,MAAM,OAAO,UAAU,IAAI;gBAAG;gBAC9C;oBAAE,MAAM;oBAAQ,MAAM,OAAO,IAAI,IAAI;gBAAG;aACzC;YAED,IAAI,CAAC,eAAe,YAAY,IAAI,KAAK,aAAa;gBACpD,WAAW;oBACT,MAAM;oBACN;oBACA;gBACF;YACF;YAEA,IAAI,eAAe,YAAY,IAAI,KAAK,aAAa;gBACnD,WAAW;oBACT;oBACA;gBACF;YACF;QACF;IACF,GAAG;QAAC;KAAO;IAEX,CAAA,GAAA,+MAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,OAAO;IACb,GAAG;QAAC;KAAM;IAEV,SAAS,WAAW,OAAyB,EAAE,KAAc;QAC3D,YAAY,CAAC;YACX,MAAM,kBAAkB;mBAAI;aAAiB;YAC7C,eAAe,CAAC,SAAS,iBAAiB,MAAM,GAAG,EAAE,GAAG;gBACtD,GAAG,gBAAgB,CAAC,SAAS,iBAAiB,MAAM,GAAG,EAAE;gBACzD,GAAG,OAAO;YACZ;YAEA,OAAO;QACT;IACF;IAEA,eAAe,iBAAiB,CAAmC;QACjE,EAAE,cAAc;QAEhB,IAAI,CAAC,SAAS;YACZ,OAAO,cAAc;QACvB;QAEA,IAAI,WAAW;YACb;QACF;QAEA,MAAM,UAA8B;YAAC;gBAAE,MAAM;gBAAQ,MAAM;YAAU;SAAE;QACvE,MAAM,SAAS,MAAM,CAAA,GAAA,+GAAA,CAAA,iBAAc,AAAD,EAAE;QAEpC,IAAI,OAAO,MAAM,GAAG,GAAG;YACrB,OAAO,OAAO,CAAC,CAAC;gBACd,QAAQ,IAAI,CAAC;oBAAE,MAAM;oBAAS;gBAAM;YACtC;QACF;QAEA,MAAM,kBAAkB,WAAW;YACjC,MAAM;YACN;QACF;QAEA,OAAO;YACL,QAAQ,SAAS,MAAM;YACvB,QAAQ,UAAU;YAClB,UAAU,CAAA,GAAA,+GAAA,CAAA,kBAAe,AAAD,EAAE;YAC1B,UAAU;YACV,OAAO;YACP,QAAQ;QACV;QAEA,aAAa;QACb,SAAS,EAAE;QACX,cAAc;QAEd,QAAQ,OAAO,CAAC,eAAe;YAC7B,UAAU;YACV,OAAO,cAAc,KAAK;QAC5B;IACF;IAEA,SAAS;QACP,OAAO;YACL,QAAQ,SAAS,MAAM;YACvB,QAAQ,UAAU;YAClB,UAAU,CAAA,GAAA,+GAAA,CAAA,kBAAe,AAAD,EAAE;YAC1B,UAAU;YACV,OAAO;YACP,QAAQ;QACV;IACF;IAEA,SAAS,WAAW,OAAgB;QAClC,YAAY,CAAC,mBAAqB;mBAAI;gBAAkB;aAAQ;QAChE,OAAO;eAAI;YAAU;SAAQ;IAC/B;IAEA,SAAS,sBAAsB,CAAyC;QACtE,aAAa,EAAE,MAAM,CAAC,KAAK;IAC7B;IAEA,SAAS,iBAAiB,MAA8B;QACtD,SAAS;IACX;IAEA,SAAS;QACP,+GAAA,CAAA,WAAQ,GACJ,+GAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO,KACrB,QAAQ,IAAI,CAAC;IACnB;IAEA,SAAS,0BAA0B,CAAiB;QAClD,iBAAiB;YAAE,GAAG,aAAa;YAAE,GAAG,CAAC;QAAC;IAC5C;IAEA,SAAS,kBAAkB,MAAkC;QAC3D,IAAI,WAAW,UAAU;YACvB,OAAO,IAAI,CAAC,wCAAwC;QACtD,OAAO,IAAI,WAAW,KAAK;YACzB,OAAO,IAAI,CAAC,yBAAyB;QACvC,OAAO,IAAI,WAAW,WAAW;YAC/B,OAAO,IAAI,CAAC,iCAAiC;QAC/C;QAEA,QAAQ,OAAO,CAAC,CAAC,EAAE,OAAO,MAAM,CAAC;IACnC;IAEA,SAAS;QACP;QACA,aAAa;QACb,SAAS,EAAE;QACX,YAAY,EAAE;QACd,YAAY;QACZ,UAAU;QACV,cAAc;QACd,oBAAoB;IACtB;IAEA,SAAS,kBAAkB,OAG1B;QACC,YAAY,QAAQ,QAAQ;QAC5B,UAAU,QAAQ,MAAM;IAC1B;IAEA,SAAS;QACP,YAAY,CAAC,mBAAqB;mBAAI,iBAAiB,KAAK,CAAC,GAAG,CAAC;aAAG;QACpE,kBAAkB;YAAE,UAAU;YAAW,QAAQ;QAAU;IAC7D;IAEA,IAAI,CAAC,SAAS;QACZ,qBACE,wPAAC;YAAK,WAAU;sBACd,cAAA,wPAAC;gBAAI,WAAU;0BACb,cAAA,wPAAC;oBAAI,WAAU;8BACb,cAAA,wPAAC;wBAAI,WAAU;kCACb,cAAA,wPAAC;4BAAI,WAAU;sCAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;IAM3C;IAEA,qBACE,wPAAC;QAAK,WAAU;;YACb,+GAAA,CAAA,WAAQ,kBACP,wPAAC,6HAAA,CAAA,aAAU;gBACT,MAAM;gBACN,SAAS;gBACT,MAAM;gBACN,UAAU,+GAAA,CAAA,WAAQ;;;;;;0BAGtB,wPAAC;gBAAI,WAAU;;kCACb,wPAAC;wBACC,WAAW,CAAC,yEAAyE,EAAE,WAAW,eAAe,aAAa,CAAC;;0CAE/H,wPAAC,qHAAA,CAAA,SAAM;gCACL,SAAS;gCACT,WAAW,IAAM,cAAc;gCAC/B,SAAS;gCACT,eAAe;gCACf,SAAS;gCACT,UAAU,SAAS,MAAM,GAAG;gCAC5B,SAAS,SAAS,MAAM,GAAG,KAAK,CAAC;gCACjC,QAAQ;;;;;;0CAEV,wPAAC,mHAAA,CAAA,OAAI;gCACH,UAAU;gCACV,WAAW;gCACX,mBAAmB;;;;;;0CAErB,wPAAC,4HAAA,CAAA,YAAS;gCACR,OAAO;gCACP,WAAW,UAAU;gCACrB,cAAc;gCACd,WAAW;gCACX,eAAe;gCACf,MAAM;gCACN,OAAO;gCACP,mBAAmB;gCACnB,cAAc;gCACd,cAAc,cAAc,cAAc;gCAC1C,OAAO;gCACP,kBAAkB;0CAElB,cAAA,wPAAC,6HAAA,CAAA,aAAU;;sDACT,wPAAC,6HAAA,CAAA,aAAU;4CACT,WAAW,gHAAA,CAAA,UAAS;4CACpB,kBAAkB;4CAClB,0BAA0B;4CAC1B,QAAQ;4CACR,eAAe;4CACf,uBAAuB;;;;;;sDAEzB,wPAAC,+HAAA,CAAA,eAAY;4CACX,eAAe;4CACf,uBAAuB;4CACvB,oBAAoB,CAAC,QAAQ,GAAG,CAAC,4BAA4B;4CAC7D,qBAAqB,CAAC,QAAQ,GAAG,CAAC,6BAA6B;;;;;;;;;;;;;;;;;;;;;;;kCAKvE,wPAAC,sHAAA,CAAA,UAAO;wBACN,QAAQ,UAAU;wBAClB,aAAa,SAAS;wBACtB,aAAa;wBACb,qBAAqB;wBACrB,eAAe;wBACf,kBAAkB;wBAClB,UAAU;wBACV,QAAQ;wBACR,SAAS,IAAM,YAAY;;;;;;;;;;;;;;;;;;AAKrC"}}, {"offset": {"line": 5551, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}