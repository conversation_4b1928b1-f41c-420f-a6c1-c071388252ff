(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/node_modules_@supabase_node-fetch_browser_c467a5.js", {

"[project]/node_modules/@supabase/node-fetch/browser.js [app-client] (ecmascript, async loader)": (({ r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, g: global, __dirname }) => (() => {

__turbopack_export_value__((__turbopack_import__) => {
    return Promise.all([
  "static/chunks/node_modules_@supabase_node-fetch_browser_0534fe.js",
  "static/chunks/node_modules_@supabase_node-fetch_browser_e02946.js"
].map((chunk) => __turbopack_load__(chunk))).then(() => {
        return __turbopack_import__("[project]/node_modules/@supabase/node-fetch/browser.js [app-client] (ecmascript)");
    });
});

})()),
}]);