{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/ssr/_3222cf._.js", "server/edge/chunks/ssr/middleware_ts_8a0420._.js", "server/edge/chunks/ssr/edge-wrapper_bc7dd4.js", "server/edge/chunks/ssr/edge-wrapper_4033f5.js"], "name": "middleware", "page": "/", "matchers": [{"locale": false, "originalSource": "/s/:path*", "regexp": "^/s(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?$"}], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "ncaWFxxJZqp1B2k/Nz5jrXavcBUyfqHLiA6crRFoOQs=", "__NEXT_PREVIEW_MODE_ID": "bb23c6e9e932f0fc6d61eaa78fd292ae", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "fd76055e0972c638e1206ab27db9308c4ca1cf225b0e569ba4e450bc7a28d3eb", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "a73709c61a563de16c21430d5a64ba9bb1774c977b09995b16e0240f064192fa"}}}, "sortedMiddleware": ["/"], "functions": {}}