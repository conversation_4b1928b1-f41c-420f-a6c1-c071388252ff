{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[next]/internal/font/google/inter_59dee874.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_export_value__({\n  \"className\": \"inter_59dee874-module__9CtR0q__className\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 12, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[next]/internal/font/google/inter_59dee874.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Inter%22,%22arguments%22:[{%22subsets%22:[%22latin%22]}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'__Inter_59dee8', '__Inter_Fallback_59dee8'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;;;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe"}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 34, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/app/providers.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const PostHogProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call PostHogProvider() from the server but PostHogProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/providers.tsx\",\n    \"PostHogProvider\",\n);\nexport const ThemeProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/providers.tsx\",\n    \"ThemeProvider\",\n);\n"], "names": [], "mappings": ";;;;;;;AACO,MAAM,kBAAkB,CAAA,GAAA,+PAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,+BACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,+PAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,+BACA"}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 57, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/components/ui/toaster.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Toaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call Toaster() from the server but To<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/toaster.tsx\",\n    \"Toaster\",\n);\n"], "names": [], "mappings": ";;;;;;AACO,MAAM,UAAU,CAAA,GAAA,+PAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,uCACA"}}, {"offset": {"line": 71, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 81, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/app/layout.tsx"], "sourcesContent": ["import './globals.css'\r\nimport { PostHogProvider, ThemeProvider } from './providers'\r\nimport { Toaster } from '@/components/ui/toaster'\r\nimport { Analytics } from '@vercel/analytics/next'\r\nimport type { Metadata } from 'next'\r\nimport { Inter } from 'next/font/google'\r\n\r\nconst inter = Inter({ subsets: ['latin'] })\r\n\r\nexport const metadata: Metadata = {\r\n  title: 'Fragments by E2B',\r\n  description: \"Open-source version of Anthropic's Artifacts\",\r\n}\r\n\r\nexport default function RootLayout({\r\n  children,\r\n}: Readonly<{\r\n  children: React.ReactNode\r\n}>) {\r\n  return (\r\n    <html lang=\"en\" suppressHydrationWarning>\r\n      <PostHogProvider>\r\n        <body className={inter.className} suppressHydrationWarning>\r\n          <ThemeProvider\r\n            attribute=\"class\"\r\n            defaultTheme=\"dark\"\r\n            enableSystem\r\n            disableTransitionOnChange\r\n          >\r\n            {children}\r\n          </ThemeProvider>\r\n          <Toaster />\r\n          <Analytics />\r\n        </body>\r\n      </PostHogProvider>\r\n    </html>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AASO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,wPAAC;QAAK,MAAK;QAAK,wBAAwB;kBACtC,cAAA,wPAAC,iHAAA,CAAA,kBAAe;sBACd,cAAA,wPAAC;gBAAK,WAAW,yIAAA,CAAA,UAAK,CAAC,SAAS;gBAAE,wBAAwB;;kCACxD,wPAAC,iHAAA,CAAA,gBAAa;wBACZ,WAAU;wBACV,cAAa;wBACb,YAAY;wBACZ,yBAAyB;kCAExB;;;;;;kCAEH,wPAAC,4HAAA,CAAA,UAAO;;;;;kCACR,wPAAC,+JAAA,CAAA,YAAS;;;;;;;;;;;;;;;;;;;;;AAKpB"}}, {"offset": {"line": 153, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 165, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/app/page.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/page.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/page.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;;;;uCACe,CAAA,GAAA,+PAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA4P,GACzR,0BACA"}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 179, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 184, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 200, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/app/favicon.ico.mjs (structured image object)"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 32, height: 32 }\n"], "names": [], "mappings": ";;;;;;uCACe;IAAE,KAAA,2GAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;AAAG"}}, {"offset": {"line": 211, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 216, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/lib/duration.ts"], "sourcesContent": ["// Taken from https://github.com/upstash/ratelimit/blob/main/src/duration.ts\r\n\r\ntype Unit = 'ms' | 's' | 'm' | 'h' | 'd'\r\nexport type Duration = `${number} ${Unit}` | `${number}${Unit}`\r\n\r\n/**\r\n * Convert a human readable duration to milliseconds\r\n */\r\nexport function ms(d: Duration): number {\r\n  const match = d.match(/^(\\d+)\\s?(ms|s|m|h|d)$/)\r\n  if (!match) {\r\n    throw new Error(`Unable to parse window size: ${d}`)\r\n  }\r\n  const time = Number.parseInt(match[1])\r\n  const unit = match[2] as Unit\r\n\r\n  switch (unit) {\r\n    case 'ms': {\r\n      return time\r\n    }\r\n    case 's': {\r\n      return time * 1000\r\n    }\r\n    case 'm': {\r\n      return time * 1000 * 60\r\n    }\r\n    case 'h': {\r\n      return time * 1000 * 60 * 60\r\n    }\r\n    case 'd': {\r\n      return time * 1000 * 60 * 60 * 24\r\n    }\r\n\r\n    default: {\r\n      throw new Error(`Unable to parse window size: ${d}`)\r\n    }\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA,4EAA4E;;;;AAQrE,SAAS,GAAG,CAAW;IAC5B,MAAM,QAAQ,EAAE,KAAK,CAAC;IACtB,IAAI,CAAC,OAAO;QACV,MAAM,IAAI,MAAM,CAAC,6BAA6B,EAAE,EAAE,CAAC;IACrD;IACA,MAAM,OAAO,OAAO,QAAQ,CAAC,KAAK,CAAC,EAAE;IACrC,MAAM,OAAO,KAAK,CAAC,EAAE;IAErB,OAAQ;QACN,KAAK;YAAM;gBACT,OAAO;YACT;QACA,KAAK;YAAK;gBACR,OAAO,OAAO;YAChB;QACA,KAAK;YAAK;gBACR,OAAO,OAAO,OAAO;YACvB;QACA,KAAK;YAAK;gBACR,OAAO,OAAO,OAAO,KAAK;YAC5B;QACA,KAAK;YAAK;gBACR,OAAO,OAAO,OAAO,KAAK,KAAK;YACjC;QAEA;YAAS;gBACP,MAAM,IAAI,MAAM,CAAC,6BAA6B,EAAE,EAAE,CAAC;YACrD;IACF;AACF"}}, {"offset": {"line": 254, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 259, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/app/actions/publish.ts [app-client] (ecmascript, action)"], "sourcesContent": ["'use server'\r\n\r\nimport { Duration, ms } from '@/lib/duration'\r\nimport { Sandbox } from '@e2b/code-interpreter'\r\nimport { kv } from '@vercel/kv'\r\nimport { customAlphabet } from 'nanoid'\r\n\r\nconst nanoid = customAlphabet('1234567890abcdef', 7)\r\n\r\nexport async function publish(\r\n  url: string,\r\n  sbxId: string,\r\n  duration: Duration,\r\n  teamID: string | undefined,\r\n  accessToken: string | undefined,\r\n) {\r\n  const expiration = ms(duration)\r\n  await Sandbox.setTimeout(sbxId, expiration, {\r\n    ...(teamID && accessToken\r\n      ? {\r\n          headers: {\r\n            'X-Supabase-Team': teamID,\r\n            'X-Supabase-Token': accessToken,\r\n          },\r\n        }\r\n      : {}),\r\n  })\r\n\r\n  if (process.env.KV_REST_API_URL && process.env.KV_REST_API_TOKEN) {\r\n    const id = nanoid()\r\n    await kv.set(`fragment:${id}`, url, { px: expiration })\r\n\r\n    return {\r\n      url: process.env.NEXT_PUBLIC_SITE_URL\r\n        ? `https://${process.env.NEXT_PUBLIC_SITE_URL}/s/${id}`\r\n        : `/s/${id}`,\r\n    }\r\n  }\r\n\r\n  return {\r\n    url,\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAOA,MAAM,SAAS,CAAA,GAAA,+IAAA,CAAA,iBAAc,AAAD,EAAE,oBAAoB;AAE3C,eAAe,QACpB,GAAW,EACX,KAAa,EACb,QAAkB,EAClB,MAA0B,EAC1B,WAA+B;IAE/B,MAAM,aAAa,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE;IACtB,MAAM,8KAAA,CAAA,UAAO,CAAC,UAAU,CAAC,OAAO,YAAY;QAC1C,GAAI,UAAU,cACV;YACE,SAAS;gBACP,mBAAmB;gBACnB,oBAAoB;YACtB;QACF,IACA,CAAC,CAAC;IACR;IAEA,IAAI,QAAQ,GAAG,CAAC,eAAe,IAAI,QAAQ,GAAG,CAAC,iBAAiB,EAAE;QAChE,MAAM,KAAK;QACX,MAAM,+IAAA,CAAA,KAAE,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,GAAG,CAAC,EAAE,KAAK;YAAE,IAAI;QAAW;QAErD,OAAO;YACL,KAAK,uCACD,CAAC,QAAQ,0DAAmC,GAAG,EAAE,GAAG,CAAC;QAE3D;IACF;IAEA,OAAO;QACL;IACF;AACF"}}, {"offset": {"line": 305, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 310, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/app/actions/validate-email.ts [app-client] (ecmascript, action)"], "sourcesContent": ["'use server'\r\n\r\nexport type EmailValidationResponse = {\r\n  address: string\r\n  status: string\r\n  sub_status: string\r\n  free_email: boolean\r\n  account: string\r\n  domain: string\r\n  mx_found: boolean\r\n  did_you_mean: string | null\r\n  domain_age_days: string | null\r\n  active_in_days: string | null\r\n  smtp_provider: string | null\r\n  mx_record: string | null\r\n  firstname: string | null\r\n  lastname: string | null\r\n  gender: string | null\r\n  country: string | null\r\n  region: string | null\r\n  city: string | null\r\n  zipcode: string | null\r\n  processed_at: string\r\n}\r\n\r\nexport async function validateEmail(email: string): Promise<boolean> {\r\n  if (!process.env.ZEROBOUNCE_API_KEY) {\r\n    return true\r\n  }\r\n\r\n  const response = await fetch(\r\n    `https://api.zerobounce.net/v2/validate?api_key=${process.env.ZEROBOUNCE_API_KEY}&email=${email}&ip_address=`,\r\n  )\r\n\r\n  const responseData = await response.json()\r\n\r\n  const data = {\r\n    ...responseData,\r\n    mx_found:\r\n      responseData.mx_found === 'true'\r\n        ? true\r\n        : responseData.mx_found === 'false'\r\n          ? false\r\n          : responseData.mx_found,\r\n  } as EmailValidationResponse\r\n\r\n  switch (data.status) {\r\n    case 'invalid':\r\n    case 'spamtrap':\r\n    case 'abuse':\r\n    case 'do_not_mail':\r\n      return false\r\n  }\r\n\r\n  return true\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;AAyBO,eAAe,cAAc,KAAa;IAC/C,IAAI,CAAC,QAAQ,GAAG,CAAC,kBAAkB,EAAE;QACnC,OAAO;IACT;IAEA,MAAM,WAAW,MAAM,MACrB,CAAC,+CAA+C,EAAE,QAAQ,GAAG,CAAC,kBAAkB,CAAC,OAAO,EAAE,MAAM,YAAY,CAAC;IAG/G,MAAM,eAAe,MAAM,SAAS,IAAI;IAExC,MAAM,OAAO;QACX,GAAG,YAAY;QACf,UACE,aAAa,QAAQ,KAAK,SACtB,OACA,aAAa,QAAQ,KAAK,UACxB,QACA,aAAa,QAAQ;IAC/B;IAEA,OAAQ,KAAK,MAAM;QACjB,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;IACX;IAEA,OAAO;AACT"}}, {"offset": {"line": 343, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 347, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/.next-internal/server/app/page/actions.js"], "sourcesContent": ["__turbopack_export_value__({\n  '5664058e140f674604ef22271bddc5a7a8b87422': (...args) => Promise.resolve(require('ACTIONS_MODULE0')).then(mod => (0, mod['publish'])(...args)),\n  'b9abb214c1d81eb4fcb1a57e2ca2d748da3cbd75': (...args) => Promise.resolve(require('ACTIONS_MODULE1')).then(mod => (0, mod['validateEmail'])(...args)),\n});"], "names": [], "mappings": "AAAA,2BAA2B;IACzB,4CAA4C,CAAC,GAAG,OAAS,QAAQ,OAAO,uGAA6B,IAAI,CAAC,CAAA,MAAO,CAAC,GAAG,GAAG,CAAC,UAAU,KAAK;IACxI,4CAA4C,CAAC,GAAG,OAAS,QAAQ,OAAO,8GAA6B,IAAI,CAAC,CAAA,MAAO,CAAC,GAAG,GAAG,CAAC,gBAAgB,KAAK;AAChJ"}}, {"offset": {"line": 351, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}